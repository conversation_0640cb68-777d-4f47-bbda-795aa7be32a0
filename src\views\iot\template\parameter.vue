<template>
    <div style="padding: 6px">
        <!-- 添加或修改通用物模型对话框 -->
        <el-dialog title="编辑参数" v-model="openEdit" width="1000px" append-to-body>
            <div style="margin: -30px 0 30px; background-color: #ddd; height: 1px"></div>
            <el-row>
                <el-col :span="12"
                    style="border: 1px solid #ddd; border-radius: 5px; padding: 10px; background-color: #eee">
                    <el-form :model="queryParams" :inline="true" label-width="48px" size="default">
                        <el-form-item label="" prop="modelName">
                            <el-input v-model="queryParams.modelName" placeholder="请输入物模型名称" style="width: 160px"
                                clearable size="default" @keyup.enter.native="handleQuery" />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="info" size="default" @click="handleQuery"
                                style="padding: 5px"><el-icon><ele-Search /></el-icon>搜索</el-button>
                        </el-form-item>
                        <!-- <el-form-item>
                            <el-link underline="never" type="primary"
                                style="margin-left: 20px"><el-icon><ele-InfoFilled /></el-icon>单击应用模板</el-link>
                        </el-form-item> -->
                    </el-form>

                    <el-table v-loading="loading" :data="templateList" size="default" @row-click="rowClick"
                        highlight-current-row :border="false" :show-header="false"
                        :row-style="{ backgroundColor: '#eee' }">
                        <el-table-column label="选择" width="30" align="center">
                            <template #default="scope">
                                <input type="radio" :checked="scope.row.isSelect"
                                    :disabled="scope.row.datatype == 'array' || scope.row.datatype == 'object'"
                                    name="template" />
                            </template>
                        </el-table-column>
                        <el-table-column label="名称" align="left" prop="modelName" />
                        <el-table-column label="标识符" align="left" prop="identifier" />
                        <el-table-column label="数据类型" align="center" prop="datatype" width="80">
                            <template #default="scope">
                                <!-- {{ scope.row.datatype }}
                                {{ typelist }} -->
                                <DictTag :options="typelist" :value="scope.row.datatype" />
                            </template>
                        </el-table-column>
                    </el-table>

                    <el-pagination v-show="total > 0" default
                        style="margin: 0 0 10px; background-color: #eee; justify-content: flex-end;" background
                        layout="prev, pager, next" :total="total" :page.sync="queryParams.pageNum"
                        :limit.sync="queryParams.pageSize" @size-change="onHandleSizeChange"
                        @current-change="onHandleCurrentChange" />
                </el-col>

                <el-col :span="11" :offset="1">
                    <el-form ref="DialogFormRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="参数名称" prop="name">
                            <el-input v-model="form.name" placeholder="例如：温度" style="width: 270px" size="default" />
                        </el-form-item>
                        <el-form-item label="参数标识" prop="id">
                            <el-input v-model="form.id" placeholder="例如：temperature" style="width: 270px"
                                size="default"></el-input>
                        </el-form-item>
                        <el-form-item label="参数排序" prop="order">
                            <el-input-number controls-position="right" v-model="form.order" placeholder="请输入排序"
                                type="number" style="width: 270px" size="default" />
                        </el-form-item>

                        <el-form-item label="参数特性" prop="property">
                            <el-checkbox name="isChart" label="图表展示" @change="isChartChange" v-model="form.isChart"
                                :true-value="1" :false-label="0"></el-checkbox>
                            <el-checkbox name="isMonitor" label="实时监测" @change="isMonitorChange"
                                v-model="form.isMonitor" :true-value="1" :false-label="0"></el-checkbox>
                            <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                v-model="form.isReadonly" :true-value="1" :false-label="0"></el-checkbox>
                            <el-checkbox name="isHistory" label="历史存储" v-model="form.isHistory" :true-value="1"
                                :false-label="0"></el-checkbox>
                            <!-- <el-checkbox name="isSharePerm" label="分享权限" v-model="form.isSharePerm" :true-value="1"
                                :false-label="0"></el-checkbox> -->
                        </el-form-item>

                        <div style="margin-bottom: 20px; background-color: #ddd; height: 1px"></div>
                        <el-form-item label="数据类型" prop="datatype">
                            <el-select v-model="form.datatype" placeholder="请选择数据类型" style="width: 155px"
                                size="default">
                                <el-option key="integer" label="整数" value="integer"></el-option>
                                <el-option key="decimal" label="小数" value="decimal"></el-option>
                                <el-option key="bool" label="布尔" value="bool" :disabled="form.isChart == 1"></el-option>
                                <el-option key="enum" label="枚举" value="enum" :disabled="form.isChart == 1"></el-option>
                                <el-option key="string" label="字符串" value="string"
                                    :disabled="form.isChart == 1"></el-option>
                            </el-select>
                        </el-form-item>
                        <div v-if="form.datatype == 'integer' || form.datatype == 'decimal'">
                            <el-form-item label="取值范围">
                                <el-row>
                                    <el-col :span="10">
                                        <el-input v-model="form.specs.min" placeholder="最小值" type="number"
                                            size="default" />
                                    </el-col>
                                    <el-col :span="4" align="center">到</el-col>
                                    <el-col :span="10">
                                        <el-input v-model="form.specs.max" placeholder="最大值" type="number"
                                            size="default" />
                                    </el-col>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="单位">
                                <el-input v-model="form.specs.unit" placeholder="例如：℃" style="width: 308px"
                                    size="default" />
                            </el-form-item>
                            <el-form-item label="步长">
                                <el-input-number controls-position="right" v-model="form.specs.step" placeholder="例如：1"
                                    type="number" style="width: 308px" size="default" />
                            </el-form-item>
                        </div>
                        <div v-if="form.datatype == 'bool'">
                            <el-form-item label="布尔值" prop="">
                                <el-row style="margin-bottom: 10px">
                                    <el-col :span="10">
                                        <el-input v-model="form.specs.falseText" placeholder="例如：关闭" size="default" />
                                    </el-col>
                                    <el-col :span="10" :offset="1">（0 值对应文本）</el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="10">
                                        <el-input v-model="form.specs.trueText" placeholder="例如：打开" size="default" />
                                    </el-col>
                                    <el-col :span="10" :offset="1">（1 值对应文本）</el-col>
                                </el-row>
                            </el-form-item>
                        </div>
                        <div v-if="form.datatype == 'enum'">
                            <el-form-item label="展示方式">
                                <el-select v-model="form.specs.showWay" placeholder="请选择展示方式" style="width: 175px">
                                    <el-option key="select" label="下拉框" value="select"></el-option>
                                    <el-option key="button" label="按钮" value="button"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="枚举项" prop="">
                                <el-row v-for="(item, index) in form.specs.enumList" :key="'enum' + index"
                                    style="margin-bottom: 10px">
                                    <el-col :span="8">
                                        <el-input v-model="item.value" placeholder="例如：0" size="default" />
                                    </el-col>
                                    <el-col :span="11" :offset="1">
                                        <el-input v-model="item.text" placeholder="例如：中速挡位" size="default" />
                                    </el-col>
                                    <el-col :span="3" :offset="1" v-if="index != 0"><a style="color: #f56c6c"
                                            @click="removeEnumItem(index)">删除</a></el-col>
                                </el-row>
                                <div>
                                    +
                                    <a style="color: #409eff" @click="addEnumItem()">添加枚举项</a>
                                </div>
                            </el-form-item>
                        </div>
                        <div v-if="form.datatype == 'string'">
                            <el-form-item label="最大长度" prop="">
                                <el-row>
                                    <el-col :span="10">
                                        <el-input v-model="form.specs.maxLength" placeholder="例如：1024" type="number"
                                            size="default" />
                                    </el-col>
                                </el-row>
                            </el-form-item>
                        </div>
                    </el-form>
                </el-col>
            </el-row>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="submitForm(DialogFormRef)" size="default">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, watch, onMounted} from 'vue';
import { listTemplate } from '/@/api/iot/template';
import DictTag from '/@/components/DictTag/index.vue'
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { FormInstance } from 'element-plus';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义组件的props
const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({
            index: undefined as any,
            parameter: {}
        }),
    },
});

// 定义自定义事件
const emit = defineEmits(['dataEvent']);
const DialogFormRef = ref();
// 数据定义
const loading = ref(true);
const total = ref(0);
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface TemplateTypeOption {
    isSelect: boolean;
    modelId: string;
}
const typelist = ref<TypeOption[]>([]);
const templateList = ref<TemplateTypeOption[]>([]);
const openEdit = ref(false);
const index = ref(-1);
let form = ref({
    name: null,
    id: null,
    order: 0,
    datatype: 'integer',
    isChart: 0,
    isHistory: 1,
    // isSharePerm: 0,
    isRequired: 1 as any,//必填数据
    isUnique: 1 as any, //
    isMonitor: 0,
    isReadonly: 0,
    specs: {
        enumList: [
            {
                value: '',
                text: '',
            },

        ],
        showWay: 'select', // 显示方式select=下拉选择框，button=按钮
        arrayType: 'integer',
        arrayCount: 5,
        params: [] as any,
        min: '',
        max: '',
        unit: '',
        step: undefined,
        falseText: '',
        trueText: '',
        maxLength: '',
        type: ''
    },
});
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: null,
    type: null,
    modelName: '',
});

// 表单校验规则
const rules = {
    name: [
        {
            required: true,
            message: '参数名称不能为空',
            trigger: 'blur',
        },
    ],
    id: [
        {
            required: true,
            message: '参数标识符不能为空',
            trigger: 'blur',
        },
    ],
    order: [
        {
            required: true,
            message: '模型排序不能为空',
            trigger: 'blur',
        },
    ],
    datatype: [
        {
            required: true,
            message: '数据类型不能为空',
            trigger: 'change',
        },
    ],
};


// 初始化模板列表
const getList = async () => {
    loading.value = true;
    try {
        const response = await listTemplate(queryParams);

        // 检查 response.rows 是否存在且是数组
        if (Array.isArray(response.data.rows)) {
            templateList.value = response.data.rows.map((row: any) => ({
                ...row,
                isSelect: false,
            }));
        } else {
            console.error('response.rows is not an array:', response.data.rows);
            templateList.value = []; // 可以设置为空数组，避免后续出现错误
        }

        total.value = response.data.total;
        setRadioSelected(props.modelValue.productId);
    } catch (error) {
        console.error('Error fetching template list:', error);
        templateList.value = []; // 出现错误时清空模板列表
    } finally {
        loading.value = false;
    }
};


// 设置单选按钮选中
const setRadioSelected = (modelId: string) => {
    templateList.value.forEach(item => {
        item.isSelect = item.modelId === modelId;
    });
};

// 单选数据处理
const rowClick = (item: any) => {
    if (item && item.datatype !== 'array' && item.datatype !== 'object') {
        form.value.name = item.modelName;
        form.value.id = item.identifier;
        form.value.order = item.modelOrder;
        form.value.isChart = item.isChart
        form.value.isHistory = item.isHistory
        // form.value.isSharePerm = item.isSharePerm || 0;
        form.value.isUnique = item.isUnique
        form.value.isRequired = item.isRequired
        form.value.isReadonly = item.isReadonly
        form.value.isMonitor = item.isMonitor
        form.value.datatype = item.datatype;
        form.value.specs = JSON.parse(item.specs);
        console.log(form.value, '---');

        if (!form.value.specs.enumList) {
            form.value.specs.enumList = [
                {
                    value: '',
                    text: '',
                },
            ];
        }
        if (!form.value.specs.arrayType) {
            form.value.specs.arrayType = 'integer';
        }
        setRadioSelected(item.modelId);
    }
};

// 取消按钮
const cancel = () => {
    openEdit.value = false;
    reset();
};

// 表单重置
const reset = () => {

    index.value = -1;
    form.value = {
        name: null,
        id: null,
        order: 0,
        datatype: 'integer',
        isChart: 0,
        isHistory: 1,
        // isSharePerm: 0,
        isUnique: 1,
        isRequired: 1,
        isMonitor: 0,
        isReadonly: 0,
        specs: {
            enumList: [
                {
                    value: '',
                    text: '',
                },

            ],
            showWay: 'select', // 显示方式select=下拉选择框，button=按钮
            arrayType: 'integer',
            arrayCount: 5,
            params: [] as any,
            min: '',
            max: '',
            unit: '',
            step: undefined,
            falseText: '',
            trueText: '',
            maxLength: '',
            type: ''
        },
    }
};

// 提交表单
import { toRaw } from 'vue';

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            try {
                form.value.datatype = formatThingsSpecs();
                delete (form.value as any).specs;
                openEdit.value = false;
                // 将响应式对象转换为普通对象
                const data = {
                    parameter: JSON.parse(JSON.stringify(toRaw(form.value))), // 使用 toRaw 转换
                    index: index.value,
                };
                console.log(data, 'data');

                emit('dataEvent', data);
                reset();
            } catch (error) {
                console.error('获取数据时发生错误:', error);
            }
        }
    });
};

const handleQuery = () => {
    queryParams.pageNum = 1;
    getList();
}
// 格式化物模型
const formatThingsSpecs = () => {
    const data: any = { type: form.value.datatype };
    if (form.value.datatype === 'integer' || form.value.datatype === 'decimal') {
        data.min = Number(form.value.specs.min || 0);
        data.max = Number(form.value.specs.max || 100);
        data.unit = form.value.specs.unit || '';
        data.step = Number(form.value.specs.step || 1);
    } else if (form.value.datatype === 'string') {
        data.maxLength = Number(form.value.specs.maxLength || 1024);
    } else if (form.value.datatype === 'bool') {
        data.falseText = form.value.specs.falseText || '关闭';
        data.trueText = form.value.specs.trueText || '打开';
    } else if (form.value.datatype === 'array') {
        data.arrayType = form.value.specs.arrayType;
    } else if (form.value.datatype === 'enum') {
        data.showWay = form.value.specs.showWay;
        if (form.value.specs.enumList && form.value.specs.enumList[0].text !== '') {
            data.enumList = form.value.specs.enumList;
        } else {
            data.showWay = 'select';
            data.enumList = [
                { value: '0', text: '低' },
                { value: '1', text: '高' },
            ];
        }
    }
    return data;
};
// 是否图表展示改变
const isChartChange = () => {
    if (form.value.isChart == 1) {
        form.value.isReadonly = 1;
    } else {
        form.value.isMonitor = 0;
    }
}
// 是否实时监测改变
const isMonitorChange = () => {
    if (form.value.isMonitor == 1) {
        form.value.isReadonly = 1;
        form.value.isChart = 1;
    }
}
// 是否只读数据改变
const isReadonlyChange = () => {
    if (form.value.isReadonly == 0) {
        form.value.isMonitor = 0;
        form.value.isChart = 0;
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        typelist.value = await dictStore.fetchDict('iot_data_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 添加枚举项
const addEnumItem = () => {
    form.value.specs.enumList.push({ value: '', text: '' });
};

// 删除枚举项
const removeEnumItem = (index: number) => {
    form.value.specs.enumList.splice(index, 1);
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    queryParams.pageSize = val;
    getList();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    queryParams.pageNum = val;
    getList();
};

// 监听props的变化
watch(() => props.modelValue.componentKey, (newVal) => {
    try {
        index.value = props.modelValue.index;
        if (newVal && newVal.index !== null) {
            // 只在有实际需要时更新 form 和打开弹窗
            openEdit.value = true;
            // 处理数据填充逻辑
            // form.value = {
            //     name: newVal.parameter.name || '',
            //     id: newVal.parameter?.id || '',
            //     order: newVal.parameter?.order || 0,
            //     isChart: newVal.parameter?.isChart || 0,
            //     isHistory: newVal.parameter?.isHistory || 1,
            //     // isSharePerm: newVal.parameter?.isSharePerm || 0,
            //     isUnique : props.modelValue.parameter?.isUnique || 1,
            //     isRequired : props.modelValue.parameter?.isRequired || 1,
            //     isMonitor: newVal.parameter?.isMonitor || 0,
            //     isReadonly: newVal.parameter?.isReadonly || 0,
            //     specs: newVal.parameter?.datatype || {},
            //     datatype: newVal.parameter?.datatype?.type || '',
            // };

            form.value.name = props.modelValue.parameter.name || '',
                form.value.id = props.modelValue.parameter?.id || '',
                form.value.order = props.modelValue.parameter?.order || 0,
                form.value.isChart = props.modelValue.parameter?.isChart || 0,
                form.value.isHistory = props.modelValue.parameter?.isHistory || 1,
                // form.value.isSharePerm = props.modelValue.parameter?.isSharePerm || 0,
                form.value.isUnique = props.modelValue.parameter?.isUnique || 1,
                form.value.isRequired = props.modelValue.parameter?.isRequired || 1,
                form.value.isMonitor = props.modelValue.parameter?.isMonitor || 0,
                form.value.isReadonly = props.modelValue.parameter?.isReadonly || 0,
                form.value.specs = props.modelValue.parameter?.datatype || {},
                form.value.datatype = props.modelValue.parameter?.datatype?.type || ''
            // 处理枚举和数组类型
            if (!form.value.specs.enumList) {
                form.value.specs.enumList = [{ value: '', text: '' }];
            }
            if (!form.value.specs.arrayType) {
                form.value.specs.arrayType = 'integer';
            }
            // console.log(form, 'form');
        }
        getList();
    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
// 生命周期钩子
onMounted(() => {
    getList();
    reset();
    getdictdata()
});
</script>
