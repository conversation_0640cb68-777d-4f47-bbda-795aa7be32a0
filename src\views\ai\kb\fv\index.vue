<template>
  <div class="file-vectorization-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">文件向量化</h2>
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>知识库管理</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentKnowledgeBase?.name || '文件向量化' }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>

      <div class="header-actions">
        <el-select
          v-model="selectedKbId"
          placeholder="选择知识库"
          @change="handleKbChange"
          style="width: 200px"
        >
          <el-option
            v-for="kb in knowledgeBases"
            :key="kb.id"
            :label="kb.name"
            :value="kb.id"
          />
        </el-select>



        <el-button
          :icon="Setting"
          @click="showSettings = !showSettings"
        >
          设置
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 主内容区域 -->
      <div class="content-area">
        <!-- 空状态提示 -->
        <div v-if="!selectedKbId" class="empty-state">
          <el-empty description="请先选择一个知识库">
            <el-button type="primary" @click="loadKnowledgeBases">刷新知识库列表</el-button>
          </el-empty>
        </div>

        <!-- 解析状态页面 -->
        <DocumentParseStatus
          v-else
          ref="parseStatusRef"
          :knowledge-base-id="selectedKbId"
          :documents="selectedDocuments"
          @status-change="handleStatusChange"
          @parse-complete="handleParseComplete"
          @parse-error="handleParseError"
        />
      </div>
    </div>

    <!-- 设置面板 -->
    <el-drawer
      v-model="showSettings"
      title="文件向量化设置"
      direction="rtl"
      size="400px"
    >
      <div class="settings-content">
        <el-form label-width="120px">
          <el-form-item label="默认解析器">
            <el-select v-model="defaultParser">
              <el-option
                v-for="parser in parserOptions"
                :key="parser.value"
                :label="parser.label"
                :value="parser.value"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <el-divider />

        <div class="settings-actions">
          <el-button @click="resetSettings">重置设置</el-button>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import {
  Setting
} from '@element-plus/icons-vue';

// 导入组件
import DocumentParseStatus from '/@/components/FileManagement/DocumentParseStatus.vue';

// 导入API
import {
  getKnowledgeBaseList,
  type KnowledgeBaseInfo
} from '/@/api/iot/knowledgeBase';
import {
  getParserOptions,
  type DocumentInfo
} from '/@/api/iot/document';

// 响应式数据
const selectedKbId = ref('');
const knowledgeBases = ref<KnowledgeBaseInfo[]>([]);
const currentKnowledgeBase = ref<KnowledgeBaseInfo | null>(null);

// UI状态
const showSettings = ref(false);

// 文档相关
const selectedDocuments = ref<DocumentInfo[]>([]);

// 操作状态

// 设置
const defaultParser = ref('naive');

// 组件引用
const parseStatusRef = ref();

// 解析器选项
const parserOptions = getParserOptions();




// 方法
const loadKnowledgeBases = async () => {
  try {
    const response = await getKnowledgeBaseList({
      page: 1,
      page_size: 100
    });

    const businessData = response.data;

    if (businessData.code === 200 && businessData.data) {
      knowledgeBases.value = businessData.data;

      // 如果有知识库且没有选中的，默认选择第一个
      if (knowledgeBases.value.length > 0 && !selectedKbId.value) {
        const firstKb = knowledgeBases.value[0];
        if (firstKb.id) {
          selectedKbId.value = firstKb.id;
          // 设置当前知识库信息
          currentKnowledgeBase.value = firstKb;
        }
      }
    } else {
      ElMessage.error(businessData.msg || businessData.message || '获取知识库列表失败');
    }
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    ElMessage.error('获取知识库列表失败');
  }
};

const handleKbChange = (kbId: string) => {
  const kb = knowledgeBases.value.find(k => k.id === kbId);
  currentKnowledgeBase.value = kb || null;

  // 清空选中的文档
  selectedDocuments.value = [];

  // 刷新解析状态页面
  if (parseStatusRef.value) {
    parseStatusRef.value.refreshStatus();
  }
};

const handleStatusChange = (documents: DocumentInfo[]) => {
  selectedDocuments.value = documents;
};

const handleParseComplete = (document: DocumentInfo) => {
  ElNotification({
    title: '解析完成',
    message: `文档 "${document.name}" 解析完成`,
    type: 'success'
  });
};

const handleParseError = (document: DocumentInfo, error: string) => {
  ElNotification({
    title: '解析失败',
    message: `文档 "${document.name}" 解析失败: ${error}`,
    type: 'error'
  });
};



const resetSettings = () => {
  defaultParser.value = 'naive';
  ElMessage.success('设置已重置');
};

const saveSettings = () => {
  // 保存设置到本地存储
  const settings = {
    defaultParser: defaultParser.value
  };

  localStorage.setItem('fileVectorizationSettings', JSON.stringify(settings));
  ElMessage.success('设置已保存');
  showSettings.value = false;
};

const loadSettings = () => {
  const saved = localStorage.getItem('fileVectorizationSettings');
  if (saved) {
    try {
      const settings = JSON.parse(saved);
      defaultParser.value = settings.defaultParser ?? 'naive';
    } catch (error) {
      console.error('加载设置失败:', error);
    }
  }
};

// 生命周期
onMounted(() => {
  loadSettings();
  loadKnowledgeBases();
});
</script>

<style scoped>
.file-vectorization-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  margin: 20px 20px 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.breadcrumb {
  font-size: 12px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}



/* 快速操作按钮组样式 */
.quick-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding: 0 20px 20px 20px;
}

/* 主内容区域样式 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 空状态样式 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.settings-content {
  padding: 20px;
}

.settings-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .quick-actions {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
    flex-wrap: wrap;
  }



  .quick-actions {
    order: 2;
    width: 100%;
    justify-content: center;
  }

  .main-content {
    padding: 0 12px 12px 12px;
  }
}


</style>
