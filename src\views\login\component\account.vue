<template>
	<el-form :model="state.ruleForm" :rules="state.rules" ref="ruleFormRef" :validate-on-rule-change="false"
		size="large" class="login-content-form">
		<el-form-item class="login-animation1" prop="username">
			<el-input text :placeholder="$t('message.account.accountPlaceholder1')" v-model="state.ruleForm.username"
				clearable autocomplete="off">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="password">
			<el-input :type="state.isShowPassword ? 'text' : 'password'"
				:placeholder="$t('message.account.accountPlaceholder2')" v-model="state.ruleForm.password"
				autocomplete="off">
				<template #prefix>
					<el-icon class="el-input__icon"><ele-Unlock /></el-icon>
				</template>
				<template #suffix>
					<i class="iconfont el-input__icon login-content-password"
						:class="state.isShowPassword ? 'icon-yincangmima' : 'icon-xianshimima'"
						@click="state.isShowPassword = !state.isShowPassword">
					</i>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation3" prop="code">
			<el-col :span="17">
				<el-input text maxlength="4" :placeholder="$t('message.account.accountPlaceholder3')"
					v-model="state.ruleForm.code" clearable autocomplete="off">
					<template #prefix>
						<el-icon class="el-input__icon"><ele-Position /></el-icon>
					</template>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="6">
				<el-button class="login-content-code" v-waves>
					<!-- <img @click="getcode" :src="codeUrl" alt="" width="100%" height="100%" style=" object-fit: cover;"> -->
					<el-image @click="getcode" :src="codeUrl" :fit="fits" />
				</el-button>
			</el-col>
		</el-form-item>
		<el-form-item class="login-animation4">
			<el-button type="primary" class="login-content-submit" round v-waves @click="onSignIn(ruleFormRef)"
				:loading="state.loading.signIn">
				<span>{{ $t('message.account.accountBtnText') }}</span>
			</el-button>
		</el-form-item>
	</el-form>
</template>

<script setup lang="ts" name="loginAccount">
import { reactive, computed, ref, nextTick, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, FormInstance } from 'element-plus';
import { useI18n } from 'vue-i18n';
import Cookies from 'js-cookie';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { getCodeImg } from '/@/api/login/index';
import type { ImageProps } from 'element-plus'
import { useUserInfo } from '/@/stores/userInfo';

// 定义变量内容
const { t } = useI18n();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
const codeUrl = ref('')
const userStore = useUserInfo();
const fits = 'contain' as ImageProps['fit']
const state = reactive({
	isShowPassword: false,
	ruleForm: {
		username: 'admin',
		password: '123456',
		code: '',
		uuid: '',
	},
	rules: {
		username: [
			{ required: true, message: '请输入用户名', trigger: 'blur' },
		],
		password: [
			{ required: true, message: '请输入密码', trigger: 'change' }
		],
		code: [
			{ required: true, message: '请输入验证码', trigger: 'change' }
		]
	},
	loading: {
		signIn: false,
	},
});



const ruleFormRef = ref<FormInstance>()


// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});
// 点击登录按钮
const onSignIn = async (formEl: FormInstance | undefined) => {
	// 表单校验
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			try {
				// 调用vuex中的login方法
				state.loading.signIn = true
				const userInfo = await userStore.login(state.ruleForm)
				const token = userInfo.data.token
				// // 存储 token 到浏览器缓存
				Session.set('token', token);
				// // 模拟数据，对接接口时，记得删除多余代码及对应依赖的引入。用于 `/src/stores/userInfo.ts` 中不同用户登录判断（模拟数据）
				Cookies.set('username', state.ruleForm.username);
				// return		
				if (!themeConfig.value.isRequestRoutes) {
					// 前端控制路由，2、请注意执行顺序
					const isNoPower = await initFrontEndControlRoutes();
					signInSuccess(isNoPower);
				} else {
					// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
					// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
					const isNoPower = await initBackEndControlRoutes();
					// 执行完 initBackEndControlRoutes，再执行 signInSuccess
					signInSuccess(isNoPower);
				}
			} catch (err) {
				state.loading.signIn = false
				formEl.clearValidate()
				getcode()


			}
		} else {
			console.log('error submit!', fields)
		}
	})
	// state.loading.signIn = true;
	// // 存储 token 到浏览器缓存
	// Session.set('token', Math.random().toString(36).substr(0));
	// // 模拟数据，对接接口时，记得删除多余代码及对应依赖的引入。用于 `/src/stores/userInfo.ts` 中不同用户登录判断（模拟数据）
	// Cookies.set('username', state.ruleForm.username);
	// if (!themeConfig.value.isRequestRoutes) {
	// 	// 前端控制路由，2、请注意执行顺序
	// 	const isNoPower = await initFrontEndControlRoutes();
	// 	signInSuccess(isNoPower);
	// } else {
	// 	// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
	// 	// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
	// 	const isNoPower = await initBackEndControlRoutes();
	// 	// 执行完 initBackEndControlRoutes，再执行 signInSuccess
	// 	signInSuccess(isNoPower); 
	// }
};
// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = currentTime.value;
		// 登录成功，跳到转首页
		// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
		if (route.query?.redirect) {
			router.push({
				path: <string>route.query?.redirect,
				query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
			});
		} else {
			router.push('/');
		}
		// 登录成功提示
		const signInText = t('message.signInText');
		ElMessage.success(`${currentTimeInfo},${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}
	state.loading.signIn = false;
};
// 获取验证码图片
const getcode = () => {
	getCodeImg().then((res) => {
		console.log('验证码API响应:', res);

		// 检查响应结构
		if (!res || !res.data) {
			console.error('验证码API响应结构异常:', res);
			return;
		}

		// 根据实际的响应结构处理数据
		const responseData = res.data.data || res.data;

		if (responseData && responseData.img && responseData.uuid) {
			codeUrl.value = 'data:image/gif;base64,' + responseData.img;
			state.ruleForm.uuid = responseData.uuid;
			state.ruleForm.code = '';
			console.log('验证码更新成功');
		} else {
			console.error('验证码数据结构不正确:', responseData);
		}
	}).catch((err) => {
		console.error('获取验证码失败:', err);
	});
};
getcode()
</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;

	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;

		&:hover {
			color: #909399;
		}
	}

	.login-content-code {
		width: 100%;
		padding: 0;
		font-weight: bold;
		letter-spacing: 5px;
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}
}
</style>
