# Java权限适配器系统文档

## 概述

Java权限适配器系统是为了实现FastAPI应用与现有Java权限系统的无缝集成而设计的。该系统允许FastAPI应用直接使用Java系统的JWT token进行用户认证，并复用Java系统的权限控制机制。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Java系统      │    │  FastAPI应用    │    │   Redis缓存     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ JWT Token   │ │───▶│ │Java Adapter │ │───▶│ │ 用户信息    │ │
│ │ 生成        │ │    │ │             │ │    │ │ 缓存        │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │ 权限数据    │ │    │ │ 权限中间件  │ │    │                 │
│ │ 存储        │ │    │ │             │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心组件

### 1. JavaAdapter (java_adapter.py)

**功能**: 处理Java系统JWT token的认证和用户数据转换

**主要方法**:
- `authenticate_java_token(token: str)`: 认证Java JWT token
- `_parse_java_json(json_str: str)`: 解析Java特有的JSON格式
- `_convert_java_user_to_fba(java_user: Dict)`: 转换Java用户数据为FBA格式

**特性**:
- 支持Java JWT token的多种格式（标准JWT和兼容性解析）
- 处理Java特有的集合序列化格式（Set[], List[], HashSet[]等）
- 自动转换Java用户数据结构为FastAPI应用格式

### 2. Java权限装饰器 (java_permission.py)

**功能**: 提供路由级别的权限控制装饰器和依赖注入

**主要装饰器**:
- `@require_java_permission(permission_code)`: 要求特定权限
- `@require_java_menu_access(menu_path)`: 要求菜单访问权限

**依赖注入函数**:
- `check_java_permission(request, permission_code)`: 检查权限
- `check_java_menu_access(request, menu_path)`: 检查菜单访问权限

### 3. Java权限中间件 (java_permission_middleware.py)

**功能**: 全局请求拦截，处理Java token认证

**处理流程**:
1. 检测请求头中的Authorization token
2. 判断是否为Java系统token
3. 调用JavaAdapter进行认证
4. 将用户信息存储到request.state中

### 4. Java权限服务 (java_permission_service.py)

**功能**: 提供权限检查的业务逻辑

**主要方法**:
- `check_user_permission(user_id, permission_code)`: 检查用户权限
- `check_user_menu_access(user_id, menu_path)`: 检查菜单访问权限
- `get_user_permissions(user_id)`: 获取用户所有权限

### 5. Java数据库适配器 (java_db.py)

**功能**: 连接Java系统数据库，查询权限相关数据

## 使用方法

### 1. 在路由中使用权限装饰器

```python
from backend.common.security.java_permission import require_java_permission

@router.get("/users")
@require_java_permission("system:user:list")
async def get_users():
    # 只有拥有 system:user:list 权限的用户才能访问
    return {"users": []}
```

### 2. 使用依赖注入方式

```python
from fastapi import Depends
from backend.common.security.java_permission import check_java_permission

@router.get("/users")
async def get_users(
    request: Request,
    has_permission: bool = Depends(
        lambda req: check_java_permission(req, "system:user:list")
    )
):
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")
    return {"users": []}
```

### 3. 使用预定义的权限依赖

```python
from backend.common.security.java_permission import RequireKnowledgeBaseList

@router.get("/knowledge-bases")
async def get_knowledge_bases(
    request: Request,
    _: bool = RequireKnowledgeBaseList(request)
):
    return {"knowledge_bases": []}
```

## 配置说明

### 1. JWT密钥配置

确保FastAPI应用的JWT密钥与Java系统保持一致：

```python
# backend/core/conf.py
TOKEN_SECRET_KEY = "your-java-jwt-secret-key"
```

### 2. Redis配置

Java系统的用户信息缓存在Redis中，键格式为：`login_tokens:{uuid}`

### 3. 数据库连接

配置Java系统数据库连接，用于权限数据查询：

```python
# backend/database/java_db.py
JAVA_DATABASE_URL = "mysql://user:password@host:port/database"
```

## 权限代码规范

### 权限代码格式

权限代码采用三级结构：`模块:功能:操作`

**示例**:
- `system:user:list` - 系统用户列表查看权限
- `system:user:create` - 系统用户创建权限
- `knowledge:base:update` - 知识库更新权限
- `knowledge:base:delete` - 知识库删除权限

### 菜单路径格式

菜单路径采用URL路径格式：`/模块/子模块`

**示例**:
- `/system/user` - 系统用户管理菜单
- `/system/role` - 系统角色管理菜单
- `/knowledge/base` - 知识库管理菜单

## 错误处理

### 常见错误类型

1. **TokenError**: Token相关错误
   - Token格式错误
   - Token已过期
   - Token认证失败

2. **PermissionError**: 权限相关错误
   - 权限不足
   - 菜单访问被拒绝

3. **DatabaseError**: 数据库相关错误
   - 连接失败
   - 查询异常

### 错误响应格式

```json
{
    "detail": "权限不足，需要权限: system:user:list",
    "status_code": 403
}
```

## 日志记录

系统会记录以下关键操作：

- JWT token认证成功/失败
- 权限检查通过/失败
- 用户数据转换异常
- 数据库连接异常

**日志级别**:
- INFO: 正常操作记录
- WARNING: 权限检查失败
- ERROR: 系统异常

## 性能优化

### 1. 缓存策略

- 用户权限信息缓存30分钟
- 菜单访问权限缓存1小时
- Redis连接池复用

### 2. 数据库优化

- 权限查询使用索引
- 批量权限检查
- 连接池管理

## 安全考虑

### 1. Token安全

- JWT签名验证
- Token过期时间检查
- 防止Token重放攻击

### 2. 权限安全

- 最小权限原则
- 权限检查日志记录
- 敏感操作二次验证

## 故障排查

### 1. Token认证失败

**检查项**:
- JWT密钥是否一致
- Token格式是否正确
- Redis中是否存在用户数据
- Token是否已过期

### 2. 权限检查失败

**检查项**:
- 用户是否拥有对应权限
- 权限代码是否正确
- 数据库连接是否正常
- 权限数据是否同步

### 3. 性能问题

**检查项**:
- Redis连接是否正常
- 数据库查询是否有索引
- 缓存命中率是否正常
- 并发请求数量

## 扩展指南

### 1. 添加新的权限类型

1. 在`java_permission.py`中添加新的装饰器
2. 在`java_permission_service.py`中实现检查逻辑
3. 更新权限代码规范文档

### 2. 集成其他认证系统

1. 创建新的适配器类
2. 实现统一的认证接口
3. 更新中间件支持多种认证方式

### 3. 添加权限缓存

1. 扩展Redis缓存策略
2. 实现权限数据预加载
3. 添加缓存失效机制

## 版本历史

- v1.0.0: 初始版本，支持基本的Java token认证和权限检查
- v1.1.0: 添加菜单访问权限控制
- v1.2.0: 优化性能，添加缓存机制
- v1.3.0: 增强错误处理和日志记录

## 联系方式

如有问题或建议，请联系开发团队：
- 技术负责人: [技术负责人邮箱]
- 开发团队: [开发团队邮箱]
- 项目地址: https://git.978543210.com/IOT-RD/fastapi_best_architecture.git
