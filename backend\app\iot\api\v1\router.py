#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IoT API v1 路由注册

注册知识库管理和文档管理相关的 API 路由
"""
from fastapi import APIRouter

from backend.app.iot.api.v1 import knowledge_base, document

# 创建 IoT API v1 路由器
v1_router = APIRouter()

# 注册知识库管理路由
v1_router.include_router(knowledge_base.router, prefix='/knowledge-base', tags=['知识库管理'])

# 注册文档管理路由
v1_router.include_router(document.router, prefix='/documents', tags=['文档管理'])
