#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统权限装饰器和依赖注入
用于在路由级别进行权限控制
"""

from typing import Optional
from functools import wraps
from fastapi import Request, HTTPException, status, Depends

from backend.service.java_permission_service import java_permission_service
from backend.common.log import log


def require_java_permission(permission_code: str):
    """
    权限装饰器 - 要求特定权限
    
    :param permission_code: 权限代码 (如: system:user:list)
    :return: 装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # 从kwargs中查找request
                request = kwargs.get('request')
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="无法获取请求对象"
                )
            
            # 获取用户ID
            user_id = _get_user_id_from_request(request)
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户认证信息"
                )
            
            # 检查权限
            has_permission = await java_permission_service.check_user_permission(
                user_id, permission_code
            )
            
            if not has_permission:
                log.warning(f"权限检查失败: user_id={user_id}, permission={permission_code}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {permission_code}"
                )
            
            log.info(f"权限检查通过: user_id={user_id}, permission={permission_code}")
            
            # 权限检查通过，执行原函数
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_java_menu_access(menu_path: str):
    """
    菜单访问装饰器 - 要求特定菜单访问权限
    
    :param menu_path: 菜单路径 (如: /system/user)
    :return: 装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                request = kwargs.get('request')
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="无法获取请求对象"
                )
            
            # 获取用户ID
            user_id = _get_user_id_from_request(request)
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户认证信息"
                )
            
            # 检查菜单访问权限
            has_access = await java_permission_service.check_user_menu_access(
                user_id, menu_path
            )
            
            if not has_access:
                log.warning(f"菜单访问权限检查失败: user_id={user_id}, menu_path={menu_path}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"无权访问菜单: {menu_path}"
                )
            
            log.info(f"菜单访问权限检查通过: user_id={user_id}, menu_path={menu_path}")
            
            # 权限检查通过，执行原函数
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


async def check_java_permission(request: Request, permission_code: str) -> bool:
    """
    检查Java系统权限 (依赖注入函数)
    
    :param request: HTTP请求
    :param permission_code: 权限代码
    :return: 是否有权限
    """
    try:
        user_id = _get_user_id_from_request(request)
        if not user_id:
            return False
        
        return await java_permission_service.check_user_permission(user_id, permission_code)
        
    except Exception as e:
        log.error(f"权限检查异常: {e}")
        return False


async def check_java_menu_access(request: Request, menu_path: str) -> bool:
    """
    检查Java系统菜单访问权限 (依赖注入函数)
    
    :param request: HTTP请求
    :param menu_path: 菜单路径
    :return: 是否可以访问
    """
    try:
        user_id = _get_user_id_from_request(request)
        if not user_id:
            return False
        
        return await java_permission_service.check_user_menu_access(user_id, menu_path)
        
    except Exception as e:
        log.error(f"菜单访问权限检查异常: {e}")
        return False


def _get_user_id_from_request(request: Request) -> Optional[int]:
    """
    从请求中获取用户ID
    
    :param request: HTTP请求
    :return: 用户ID
    """
    try:
        # 从认证中间件设置的用户信息中获取
        if hasattr(request.state, 'user_id'):
            return request.state.user_id
        
        # 从用户对象中获取
        if hasattr(request, 'user') and hasattr(request.user, 'id'):
            return request.user.id
        
        # 从Java认证信息中获取
        if hasattr(request.state, 'java_user') and 'userId' in request.state.java_user:
            return request.state.java_user['userId']
        
        return None
        
    except Exception as e:
        log.error(f"获取用户ID失败: {e}")
        return None


# 常用权限依赖注入
def RequireKnowledgeBaseList(request: Request):
    """知识库列表权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:list"))

def RequireKnowledgeBaseCreate(request: Request):
    """知识库创建权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:create"))

def RequireKnowledgeBaseUpdate(request: Request):
    """知识库更新权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:update"))

def RequireKnowledgeBaseDelete(request: Request):
    """知识库删除权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:delete"))

def RequireSystemUserList(request: Request):
    """系统用户列表权限依赖"""
    return Depends(lambda: check_java_permission(request, "system:user:list"))

def RequireSystemRoleList(request: Request):
    """系统角色列表权限依赖"""
    return Depends(lambda: check_java_permission(request, "system:role:list"))
