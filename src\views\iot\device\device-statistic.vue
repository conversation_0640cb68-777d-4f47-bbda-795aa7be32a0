<template>
    <div style="padding-left: 20px">
        <el-row>
            <el-col :span="24">
                <el-form :model="state.queryParams" ref="queryForm" :inline="true" label-width="75px">
                    <el-form-item label="请选择设备从机:" label-width="120px" v-if="isSubDev">
                        <el-select v-model="state.queryParams.slaveId" placeholder="请选择设备从机" @change="selectSlave">
                            <el-option v-for="slave in slaveList" :key="slave.slaveId"
                                :label="`${slave.deviceName} (${slave.slaveId})`" :value="slave.slaveId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间范围">
                        <!-- <el-date-picker v-model="daterangeTime" size="default" style="width: 240px"
                            value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker> -->
                        <el-date-picker v-model="daterangeTime" style="width: 240px" date-format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" size="default" @click="getListHistory"><el-icon>
                                <Search />
                            </el-icon>查询</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
            <el-col :span="23">
                <div v-for="(item, index) in staticList" :key="index" style="margin-bottom: 30px">
                    <el-card shadow="hover" :body-style="{ padding: '10px 0px', overflow: 'auto' }"
                        v-loading="state.loading">
                        <div :ref="setChartRef" style="height: 300px; width: 1080px"></div>
                    </el-card>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script setup lang="ts" name="">
import { nextTick, reactive, ref, watch } from 'vue';
import * as echarts from 'echarts';
import { listHistory } from '/@/api/iot/deviceLog';
const props = defineProps({
    device: {
        type: Object,
        default: () => ({})
    }
});
/* 获取当前时间*/
const getTime = () => {
    let date = new Date();
    let y = date.getFullYear();
    let m = date.getMonth() + 1;
    let d = date.getDate();
    let H = date.getHours();
    let mm = date.getMinutes();
    let s = date.getSeconds();
    m = m < 10 ? '0' + m : m as any;
    d = d < 10 ? '0' + d : d as any;;
    H = H < 10 ? '0' + H : H as any;;
    return y + '-' + m + '-' + d 
}
const deviceInfos = ref({
    subDeviceList: [],
    staticList: [],
    deviceId: 0,
    slaveId: 0,
    serialNumber: 0,
    cacheThingsModel: {
        properties: []
    },
});// 设备信息
const state = reactive({
    loading: false,// 图表遮罩层
    queryParams: {
        serialNumber: 0 as any,
        identity: '',
        slaveId: 0,
        beginTime: '',
        endTime: '',
    },
});

const chart = ref([]) as any;// 图表集合
const staticList = ref<any>([]) as any;// 统计物模型
const daterangeTime = ref([getTime(), getTime()]) as any;// 激活时间范围


// 用于存储所有图表的 ref
const chartRefs = ref<HTMLElement[]>([]);
const slaveList = ref([]) as any;  // 子设备列表
const isSubDev = ref(false);  // 是否有子设备
// 动态设置 ref
const setChartRef = (el: any) => {
    if (el) {
        chartRefs.value.push(el);
    }
};
/* 获取监测历史数据*/
const getListHistory = () => {
    console.log(daterangeTime,'daterangeTime');
    state.loading = true;
    state.queryParams.serialNumber = state.queryParams.slaveId ? deviceInfos.value.serialNumber + '_' + state.queryParams.slaveId : deviceInfos.value.serialNumber;
    if (daterangeTime.value != null && daterangeTime.value != '') {
        state.queryParams.beginTime = daterangeTime.value[0] + ' 00:00';
        state.queryParams.endTime = daterangeTime.value[1] + ' 23:59';
    }

    listHistory(state.queryParams).then((res: any) => {
        for (let key in res.data.data) {
            for (let i = 0; i < staticList.value.length; i++) {
                if (key == staticList.value[i].id) {
                    // 对象转数组
                    let dataList = [];
                    for (let j = 0; j < res.data.data[key].length; j++) {
                        let item = [];
                        item[0] = res.data.data[key][j].time;
                        item[1] = res.data.data[key][j].value;
                        dataList.push(item);
                    }
                    console.log(dataList,'dataList');
                    
                    // 图表显示数据
                    chart.value[i].setOption({
                        series: [
                            {
                                data: dataList,
                            },
                        ],
                    });
                }
            }
        }
        state.loading = false;
    });
}
/*选择从机*/
const selectSlave = () => {
    staticList.value = deviceInfos.value.cacheThingsModel['properties'].filter((item: any) => {
        return item.tempSlaveId == state.queryParams.slaveId;
    });
    // 加载图表
    nextTick(() => {
        // 绘制图表
        getStatistic();
        // 获取统计数据
        getListHistory();
    });
}

/**监测统计数据 */
const getStatistic = () => {
    let color = ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'];
    for (let i = 0; i < staticList.value.length; i++) {
        const chartElement = chartRefs.value[i];
        // 设置宽度
        chartElement.style.width = document.documentElement.clientWidth - 510 + 'px';
        chart.value[i] = echarts.init(chartElement);
        var option;
        option = {
            animationDurationUpdate: 3000,
            tooltip: {
                trigger: 'axis',
            },
            title: {
                left: 'center',
                text: staticList.value[i].name + '统计 （单位 ' + (staticList.value[i].datatype && staticList.value[i].datatype.unit != undefined ? staticList.value[i].datatype.unit : '无') + '）',
            },
            grid: {
                top: '80px',
                left: '40px',
                right: '20px',
                bottom: '60px',
                containLabel: true,
            },
            toolbox: {
                feature: {
                    dataZoom: {
                        yAxisIndex: 'none',
                    },
                    restore: {},
                    saveAsImage: {},
                },
            },
            xAxis: {
                type: 'time',
            },
            yAxis: {
                type: 'value',
                scale: true,//自适应
                axisLabel: { formatter: function (value: number, index: any) { return value.toFixed(2); } }//可根据实际情况修改y轴保留小数位数
            },
            dataZoom: [
                {
                    type: 'inside',
                    start: 0,
                    end: 100,
                },
                {
                    start: 0,
                    end: 100,
                },
            ],
            series: [
                {
                    name: staticList.value[i].name,
                    type: 'line',
                    symbol: 'none',
                    sampling: 'lttb',
                    itemStyle: {
                        color: i > 9 ? color[0] : color[i],
                    },
                    areaStyle: {},
                    data: [],
                },
            ],
        };
        option && chart.value[i].setOption(option);
    }
}
watch(
    () => props.device,
    (newVal, oldVal) => {
        deviceInfos.value = newVal as any;
        if (deviceInfos.value && deviceInfos.value.deviceId != 0) {
            isSubDev.value = deviceInfos.value.subDeviceList && deviceInfos.value.subDeviceList.length > 0;
            state.queryParams.slaveId = deviceInfos.value.slaveId;
            state.queryParams.serialNumber = deviceInfos.value.serialNumber;
            slaveList.value = newVal.subDeviceList;
            // 监测数据
            if (isSubDev.value) {
                staticList.value = deviceInfos.value.cacheThingsModel['properties'].filter((item: any) => {
                    return item.tempSlaveId == state.queryParams.slaveId;
                });
            } else {
                staticList.value = deviceInfos.value.staticList;
            }
            // 加载图表
            nextTick(() => {
                // 绘制图表
                getStatistic();
            });
        }
    },
    { immediate: true }
);

// 暴露方法给父组件
defineExpose({
    getListHistory
});
</script>