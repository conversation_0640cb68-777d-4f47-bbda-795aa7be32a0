<template>
  <div class="layout-padding">
    <el-card shadow="hover" class="chat-main-card">
      <!-- 页面头部 -->
      <template #header>
        <div class="card-header">
          <div>
            <h2 style="margin: 0; display: flex; align-items: center; gap: 8px;">
              <el-icon><ChatDotRound /></el-icon>
              AI智能聊天
            </h2>
            <p style="margin: 8px 0 0 0; color: var(--el-text-color-regular); font-size: 14px;">
              与AI助手进行智能对话，获取专业的问题解答和建议
            </p>
          </div>
          <el-space>
            <!-- 连接状态指示器 -->
            <el-tag :type="connectionStatus.type" size="default">
              <el-icon><Connection /></el-icon>
              {{ connectionStatus.text }}
            </el-tag>
            <el-button @click="clearChat" :icon="Delete" type="danger" plain>
              清空对话
            </el-button>
            <el-button @click="exportChat" :icon="Download">
              导出对话
            </el-button>
          </el-space>
        </div>
      </template>

      <!-- 聊天主体区域 -->
      <div class="chat-container">
        <!-- 消息显示区域 -->
        <div class="chat-messages" ref="messagesContainer">
          <div v-if="messages.length === 0" class="empty-chat">
            <el-empty description="开始您的AI对话吧！">
              <template #image>
                <el-icon size="60" color="var(--el-color-primary)">
                  <ChatDotRound />
                </el-icon>
              </template>
              <el-button type="primary" @click="sendSampleMessage">
                发送示例消息
              </el-button>
            </el-empty>
          </div>

          <!-- 消息列表 -->
          <div v-for="(message, index) in messages" :key="index" class="message-item">
            <!-- 用户消息 -->
            <div v-if="message.type === 'user'" class="message-wrapper user-message">
              <div class="message-content">
                <div class="message-text">{{ message.content }}</div>
                <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              </div>
              <el-avatar class="message-avatar" :size="36">
                <el-icon><User /></el-icon>
              </el-avatar>
            </div>

            <!-- AI消息 -->
            <div v-else class="message-wrapper ai-message">
              <el-avatar class="message-avatar" :size="36" style="background-color: var(--el-color-primary);">
                <el-icon><Avatar /></el-icon>
              </el-avatar>
              <div class="message-content">
                <div class="message-text">
                  <div v-if="message.loading" class="typing-indicator">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                  <div v-else>{{ message.content }}</div>
                </div>
                <div class="message-time">{{ formatTime(message.timestamp) }}</div>
                <div class="message-actions" v-if="!message.loading">
                  <el-button size="small" link @click="copyMessage(message.content)">
                    <el-icon><CopyDocument /></el-icon>
                    复制
                  </el-button>
                  <el-button size="small" link @click="likeMessage(message)">
                    <el-icon><Star /></el-icon>
                    {{ message.liked ? '已赞' : '点赞' }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
          <el-card shadow="never" class="input-card">
            <div class="input-container">
              <el-input
                v-model="inputMessage"
                type="textarea"
                :rows="3"
                placeholder="请输入您的问题..."
                resize="none"
                @keydown.enter.exact.prevent="sendMessage"
                @keydown.enter.shift.exact="handleShiftEnter"
                :disabled="isLoading"
                class="message-input"
              />
              <div class="input-actions">
                <div class="input-tips">
                  <span>按 Enter 发送，Shift + Enter 换行</span>
                </div>
                <el-button
                  type="primary"
                  @click="sendMessage"
                  :loading="isLoading"
                  :disabled="!inputMessage.trim()"
                  class="send-button"
                >
                  <el-icon><Promotion /></el-icon>
                  发送
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="AiLlmChat">
import { ref, onMounted, nextTick, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ChatDotRound,
  Connection,
  Delete,
  Download,
  User,
  Avatar,
  CopyDocument,
  Star,
  Promotion
} from '@element-plus/icons-vue';

// 消息类型定义
interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  loading?: boolean;
  liked?: boolean;
}

// 响应式数据
const inputMessage = ref('');
const isLoading = ref(false);
const messages = ref<ChatMessage[]>([]);
const messagesContainer = ref<HTMLElement>();

// 连接状态
const connectionStatus = computed(() => {
  // 这里可以根据实际的连接状态来动态设置
  return {
    type: 'success' as const,
    text: '已连接'
  };
});

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 智能生成AI回复
const generateAIResponse = (userInput: string): string => {
  const input = userInput.toLowerCase();

  // 问候语回复
  if (input.includes('你好') || input.includes('hello') || input.includes('hi')) {
    return '您好！我是AI智能助手，很高兴为您服务。我可以帮助您解答问题、提供建议、协助分析问题等。请告诉我您需要什么帮助？';
  }

  // 功能介绍
  if (input.includes('功能') || input.includes('能力') || input.includes('可以做什么')) {
    return `我是一个智能AI助手，具备以下主要功能：

🤖 **智能对话**：可以进行自然流畅的对话交流
📚 **知识问答**：回答各种领域的问题
💡 **创意协助**：提供创意想法和解决方案
📝 **文本处理**：帮助编写、修改、总结文本
🔍 **分析建议**：对问题进行分析并提供专业建议
🛠️ **技术支持**：解答技术相关问题

请随时告诉我您需要什么帮助！`;
  }

  // 技术相关问题
  if (input.includes('vue') || input.includes('前端') || input.includes('javascript') || input.includes('typescript')) {
    return `关于前端技术，我可以为您提供专业的指导：

🔧 **Vue.js**：组件开发、状态管理、路由配置等
⚡ **性能优化**：代码分割、懒加载、缓存策略
🎨 **UI框架**：Element Plus、Ant Design等组件库使用
📱 **响应式设计**：移动端适配、媒体查询
🛠️ **工程化**：Vite、Webpack、构建优化

请具体描述您遇到的问题，我会提供详细的解决方案。`;
  }

  // 学习相关
  if (input.includes('学习') || input.includes('教程') || input.includes('如何')) {
    return `很高兴您有学习的热情！我可以为您提供学习指导：

📖 **制定学习计划**：根据您的目标和时间安排
🎯 **重点知识梳理**：突出核心概念和技能
💪 **实践建议**：推荐项目和练习方式
📚 **资源推荐**：优质的学习资料和工具
🔄 **学习方法**：高效的学习策略和技巧

请告诉我您想学习什么内容，我会为您量身定制学习建议。`;
  }

  // 问题解决
  if (input.includes('问题') || input.includes('错误') || input.includes('bug') || input.includes('报错')) {
    return `我来帮您分析和解决问题：

🔍 **问题诊断**：分析问题的根本原因
🛠️ **解决方案**：提供多种可行的解决方法
⚡ **快速修复**：优先给出最直接的解决方案
🔄 **预防措施**：避免类似问题再次发生
📋 **最佳实践**：推荐标准的开发规范

请详细描述您遇到的具体问题，包括错误信息、操作步骤等，我会为您提供针对性的解决方案。`;
  }

  // 默认智能回复
  const defaultResponses = [
    `我理解您的问题。让我为您详细分析一下：

基于您提到的内容，我建议从以下几个角度来考虑：
• 首先明确核心需求和目标
• 分析现有的资源和限制条件
• 制定可行的实施方案
• 考虑可能的风险和应对措施

您希望我重点关注哪个方面呢？`,

    `这是一个很有价值的话题。根据我的分析：

🎯 **关键要点**：
• 需要综合考虑多个因素
• 建议采用渐进式的方法
• 重视实际效果和反馈

💡 **建议方案**：
• 先从小范围试点开始
• 收集数据并持续优化
• 建立长期的改进机制

您觉得这个方向如何？有什么具体的疑问吗？`,

    `感谢您的提问！让我为您提供一些专业的见解：

📊 **现状分析**：
当前的情况需要我们仔细评估各种因素的影响

🔄 **解决思路**：
• 系统性地分析问题的各个层面
• 寻找最优的平衡点
• 制定可执行的行动计划

🚀 **下一步行动**：
建议您先确定优先级，然后逐步推进

还有什么具体的细节需要我帮您分析吗？`
  ];

  return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
};

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - timestamp;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (date.toDateString() === now.toDateString()) { // 今天
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 发送消息
const sendMessage = async () => {
  const content = inputMessage.value.trim();
  if (!content || isLoading.value) return;

  // 添加用户消息
  const userMessage: ChatMessage = {
    id: generateId(),
    type: 'user',
    content,
    timestamp: Date.now()
  };
  messages.value.push(userMessage);

  // 清空输入框
  inputMessage.value = '';

  // 滚动到底部
  scrollToBottom();

  // 设置加载状态
  isLoading.value = true;

  // 添加AI消息占位符
  const aiMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: '',
    timestamp: Date.now(),
    loading: true
  };
  messages.value.push(aiMessage);
  scrollToBottom();

  try {
    // 模拟AI响应延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // 智能模拟AI回复
    const aiResponse = generateAIResponse(content);

    // 更新AI消息
    const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...aiMessage,
        content: aiResponse,
        loading: false,
        timestamp: Date.now()
      };
    }

    scrollToBottom();
  } catch (error) {
    ElMessage.error('发送消息失败，请稍后重试');
    // 移除失败的AI消息
    const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
    if (messageIndex !== -1) {
      messages.value.splice(messageIndex, 1);
    }
  } finally {
    isLoading.value = false;
  }
};

// 处理Shift+Enter换行
const handleShiftEnter = (event: KeyboardEvent) => {
  // 允许默认的换行行为
  return true;
};

// 发送示例消息
const sendSampleMessage = () => {
  const sampleMessages = [
    '你好，请介绍一下你的功能和能力。',
    '我在学习Vue.js，有什么好的建议吗？',
    '如何提高前端开发效率？',
    '请帮我分析一下现代Web开发的趋势。'
  ];
  const randomMessage = sampleMessages[Math.floor(Math.random() * sampleMessages.length)];
  inputMessage.value = randomMessage;
  sendMessage();
};

// 复制消息
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    ElMessage.success('消息已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 点赞消息
const likeMessage = (message: ChatMessage) => {
  message.liked = !message.liked;
  ElMessage.success(message.liked ? '已点赞' : '已取消点赞');
};

// 清空对话
const clearChat = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有对话记录吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    messages.value = [];
    ElMessage.success('对话记录已清空');
  } catch {
    // 用户取消操作
  }
};

// 导出对话
const exportChat = () => {
  if (messages.value.length === 0) {
    ElMessage.warning('暂无对话记录可导出');
    return;
  }

  const chatContent = messages.value
    .filter(msg => !msg.loading)
    .map(msg => {
      const time = formatTime(msg.timestamp);
      const sender = msg.type === 'user' ? '用户' : 'AI助手';
      return `[${time}] ${sender}: ${msg.content}`;
    })
    .join('\n\n');

  const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `AI聊天记录_${new Date().toLocaleDateString('zh-CN')}.txt`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  ElMessage.success('对话记录已导出');
};

// 生命周期
onMounted(() => {
  // 添加欢迎消息
  const welcomeMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: `🎉 欢迎使用AI智能聊天助手！

我是您的专属AI助手，可以帮助您：
• 💬 进行智能对话交流
• 🔍 解答各种问题
• 💡 提供专业建议
• 📚 分享知识和经验
• 🛠️ 协助解决技术问题

请随时向我提问，我会尽力为您提供帮助！`,
    timestamp: Date.now()
  };

  messages.value.push(welcomeMessage);
  scrollToBottom();
});
</script>

<style scoped>
/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

/* 聊天主容器 */
.chat-main-card {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.chat-main-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 消息显示区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  border-bottom: 1px solid var(--el-border-color-light);
}

.empty-chat {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 消息项样式 */
.message-item {
  margin-bottom: 20px;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 80%;
}

.user-message {
  flex-direction: row-reverse;
  margin-left: auto;
}

.ai-message {
  margin-right: auto;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  background: var(--el-color-white);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 12px 16px;
  word-wrap: break-word;
  line-height: 1.5;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-message .message-text {
  background: var(--el-color-primary);
  color: white;
  border-color: var(--el-color-primary);
}

.ai-message .message-text {
  background: var(--el-color-white);
  border-color: var(--el-border-color-light);
}

.message-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

.message-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域样式 */
.chat-input-area {
  flex-shrink: 0;
  padding: 20px;
  background: var(--el-color-white);
  border-top: 1px solid var(--el-border-color-light);
}

.input-card {
  border: none;
  box-shadow: none;
}

.input-card :deep(.el-card__body) {
  padding: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-input :deep(.el-textarea__inner) {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  transition: border-color 0.3s;
}

.message-input :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-tips {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.send-button {
  padding: 8px 20px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .card-header {
    flex-direction: column;
    gap: 15px;
  }

  .message-wrapper {
    max-width: 90%;
  }

  .chat-main-card {
    height: calc(100vh - 100px);
  }
}

@media (max-width: 768px) {
  .chat-messages {
    padding: 15px;
  }

  .chat-input-area {
    padding: 15px;
  }

  .message-wrapper {
    max-width: 95%;
  }

  .message-text {
    padding: 10px 12px;
    font-size: 13px;
  }

  .input-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .send-button {
    width: 100%;
    padding: 12px;
    height: 44px;
  }

  .input-tips {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .chat-main-card {
    height: calc(100vh - 80px);
  }

  .chat-messages {
    padding: 10px;
  }

  .chat-input-area {
    padding: 10px;
  }

  .message-wrapper {
    max-width: 100%;
  }

  .message-text {
    padding: 8px 10px;
    font-size: 12px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
  }

  .send-button {
    height: 48px;
    font-size: 14px;
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}
</style>