<template>
	<div>	
		<el-card v-if="showSearch" style="margin-bottom: 10px">
			<!-- 搜索表单 -->
			<el-form
				:model="queryParams"
				ref="queryForm"
				:inline="true"
				size="mini"
				label-width="68px"
				style="margin-bottom: -18px"
				@submit.prevent="handleQuery"
			>
				<el-form-item label="设备编号" prop="deviceSipId">
					<el-input v-model="queryParams.deviceSipId" placeholder="请输入设备编号" clearable @keyup.enter="handleQuery" />
				</el-form-item>

				<el-form-item label="通道ID" prop="channelSipId">
					<el-input v-model="queryParams.channelSipId" placeholder="请输入通道ID" clearable @keyup.enter="handleQuery" />
				</el-form-item>

				<el-form-item label="状态" prop="status">
					<el-select
						v-model="queryParams.status"
						placeholder="请选择状态"
						clearable
						style="width: 200px"
						popper-class="status-select-dropdown"
						@focus="console.log('Select focused, options:', dict.type.sip_gen_status)"
					>
						<el-option v-for="dict in dict.type.sip_gen_status" :key="dict.value" :label="dict.label" :value="dict.value" />
					</el-select>
				</el-form-item>

				<el-form-item style="float: right">
					<el-button size="default" type="primary" class="ml10" @click="handleQuery">
						<el-icon>
							<ele-Search />
						</el-icon>
						搜索
					</el-button>
					<el-button size="default" @click="resetQuery">
						<el-icon><ele-Refresh /></el-icon>
						重置
					</el-button>
				</el-form-item>
			</el-form>
		</el-card>
		<el-card>
			<el-row :gutter="10" style="margin-bottom: 15px">
				<el-col :span="1.5">
					<el-button type="primary" size="mini" @click="handleAdd" :disabled="isGeneralUser"
						><el-icon><Plus /></el-icon>
						{{ '批量生成' }}
					</el-button>
					<el-button type="danger" size="mini" :disabled="multiple || isGeneralUser" @click="handleDelete"
						><el-icon><Delete /></el-icon>
						{{ '批量删除' }}
					</el-button>
				</el-col>
				<right-toolbar
					v-model:showSearch="showSearch"
					@queryTable="getList"
				></right-toolbar>
			</el-row>

			<el-table v-loading="loading" :data="sipidList" @selection-change="handleSelectionChange" @cell-dblclick="celldblclick">
				<el-table-column type="selection" :selectable="selectable" width="55" align="center" />

				<el-table-column label="设备编号" align="center" prop="deviceSipId" min-width="200">
					<template #default="scope">
						<el-link underline="never" type="primary" @click="handleViewDevice(scope.row.deviceSipId)">
							{{ scope.row.deviceSipId }}
						</el-link>
					</template>
				</el-table-column>

				<el-table-column label="通道ID" align="center" prop="channelSipId" min-width="200" />

				<el-table-column label="状态" align="center" prop="status" width="80">
					<template #default="scope">
						<dict-tag :options="dict.type.iot_device_status" :value="scope.row.status" size="mini" />
					</template>
				</el-table-column>

				<el-table-column label="所属产品" align="center" prop="productName" min-width="150" />

				<el-table-column label="设备类型" align="center" prop="deviceType" min-width="80">
					<template #default="scope">
						<dict-tag :options="dict.type.video_type" :value="scope.row.deviceType" />
					</template>
				</el-table-column>

				<el-table-column label="通道类型" align="center" prop="channelType" min-width="80">
					<template #default="scope">
						<dict-tag :options="dict.type.channel_type" :value="scope.row.channelType" />
					</template>
				</el-table-column>

				<el-table-column label="行政区域" align="center" prop="citycode" min-width="180" />

				<el-table-column label="注册时间" align="center" prop="createTime" width="200">
					<template #default="scope">
						<span style="white-space: nowrap;">{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
					</template>
				</el-table-column>

				<el-table-column fixed="right" label="操作" align="center" class-name="small-padding fixed-width" width="100">
					<template #default="scope">
						<el-button size="mini" type="text" icon="el-icon-edit" @click="handleBinding(scope.row)">
							{{ $t('设备绑定') }}
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			<el-pagination
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
				class="mt15"
				style="justify-content: flex-end"
				:pager-count="5"
				:page-sizes="[10, 20, 30]"
				v-model:current-page="queryParams.pageNum"
				background
				v-model:page-size="queryParams.pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
			>
			</el-pagination>
			<!-- 创建设备对话框 -->
			<el-dialog v-model="open" :title="title" width="450px" append-to-body>
				<el-form :model="createForm" label-width="80px">
					<el-form-item label="行政区划">
						<el-cascader :options="cityOptions" v-model="createForm.city" @change="changeProvince" :props="{ expandTrigger: 'hover' }" />
					</el-form-item>

					<el-form-item label="设备类型" prop="deviceType">
						<el-select v-model="createForm.deviceType" placeholder="请选择设备类型" v-if="dict.type.video_type.length > 0">
							<el-option 
								v-for="item in dict.type.video_type" 
								:key="item.dictValue" 
								:label="item.dictLabel" 
								:value="item.dictValue" 
							/>
						</el-select>
						<el-skeleton v-else :rows="1" animated />
					</el-form-item>

					<el-form-item label="通道类型" prop="channelType">
						<el-select v-model="createForm.channelType" placeholder="请选择通道类型" v-if="dict.type.channel_type.length > 0">
							<el-option 
								v-for="item in dict.type.channel_type" 
								:key="item.dictValue" 
								:label="item.dictLabel" 
								:value="item.dictValue" 
							/>
						</el-select>
						<el-skeleton v-else :rows="1" animated />
					</el-form-item>

					<el-form-item label="所属产品" prop="productName">
						<el-input readonly v-model="createForm.productName" placeholder="请选择产品">
							<template #append>
								<!-- <el-button @click="selectProduct">选择</el-button> -->
								<el-button @click="openProductDialog" @click.stop>选择</el-button>
							</template>
						</el-input>
					</el-form-item>

					<el-form-item label="通道数量" prop="createNum">
						<el-input-number v-model="createForm.createNum" :max="10" :min="1" style="width: 330px" />
					</el-form-item>
				</el-form>

				<template #footer>
					<el-button type="primary" @click="submitForm">确认</el-button>
					<el-button @click="cancel">取消</el-button>
				</template>
			</el-dialog>

			<!-- 绑定设备对话框 -->
			<el-dialog v-model="bindingOpen" :title="title" width="450px" append-to-body>
				<el-form :model="form" label-width="80px">
					<el-form-item label="关联设备">
						<el-select v-model="form.reDeviceId" filterable placeholder="请选择设备">
							<el-option v-for="item in deviceList" :key="item.deviceId" :label="item.deviceName" :value="item.deviceId" />
						</el-select>
					</el-form-item>

					<el-form-item label="关联场景">
						<el-select v-model="form.reSceneModelId" filterable placeholder="请选择场景">
							<el-option v-for="item in sceneList" :key="item.sceneModelId" :label="item.sceneModelName" :value="item.sceneModelId" />
						</el-select>
					</el-form-item>
				</el-form>

				<template #footer>
					<el-button type="primary" @click="submitFormBinding">确认绑定</el-button>
					<el-button @click="cancelBinding">取消</el-button>
				</template>
			</el-dialog>
		</el-card>
		<ProductList ref="productDialogVisible" @productEvent="getProductData" />
	</div>
</template>

<script setup>
import { reactive, ref, onMounted ,defineAsyncComponent} from 'vue';
import { listChannel ,delChannel} from '/@/api/iot/channel';
import { useDictStore } from '/@/stores/dictStore';
import { ElMessageBox, ElMessage } from 'element-plus';
import { regionData } from 'element-china-area-data';

const ProductList = defineAsyncComponent(() => import('/@/views/iot/sip/product-list.vue'));

// ✅ 弹窗显示状态
const productDialogVisible = ref(null);

// 显示搜索条件
const showSearch = ref(true);
const key = ref(0);

// 是否为普通用户
const isGeneralUser = ref(false);

//字典
const dictStore = useDictStore();

// 遮罩层
const loading = ref(true);

// 选中数组
const ids = ref([]);
// 非多个禁用
const multiple = ref(true);
// 总条数
const total = ref(0);

const form = ref({}); // 绑定设备表单
const createForm = ref({
	productId: null,
    productName: '',
    tenantId: null,
    tenantName: '',
	city: '',
	citycode: '',
	deviceType: '',
	channelType: '',
	createNum: 1,
	remark: '',
	area: '',
}); // 创建设备表单

// sipid表格数据
const sipidList = ref([]);
// 弹出层标题
const title = ref('');
// 是否显示弹出层
const open = ref(false);
// 绑定弹窗
const bindingOpen = ref(false);
// 设备列表
const deviceList = ref([]);
// 场景列表
const sceneList = ref([]);

// 字典数据
const dict = reactive({
	type: {
		sip_gen_status: [],
		iot_device_status: [],
		video_type: [],
		channel_type: [],
	},
});

// 查询参数
const queryParams = reactive({
	pageNum: 1,
	pageSize: 10,
	deviceSipId: '',
	channelSipId: '',
	status: '',
});

//查询通道列表

const getList = async () => {
	loading.value = true;
	try {
		const res = await listChannel(queryParams);

		if (res && res.data) {
			// 处理不同格式的API响应
			const responseData = res.data;

			// 情况1: 直接返回数组
			if (Array.isArray(responseData)) {
				sipidList.value = [...responseData];
				total.value = responseData.length;
			}
			// 情况2: 标准分页格式 { code, rows, total }
			else if (responseData.code === 200 && responseData.rows) {

				if (typeof responseData.total !== 'undefined') {
					total.value = responseData.total;
				} else {
					total.value = responseData.rows.length;
				}

				sipidList.value = [...responseData.rows];
			}
			// 情况3: 其他格式
			else {
				console.warn('未识别的API响应格式:', responseData);
				sipidList.value = [];
				total.value = 0;
			}
		} else {
			console.error('API返回数据异常:', res);
			sipidList.value = [];
			total.value = 0;
		}
	} catch (error) {
		console.error('获取SIP通道列表失败:', error);
		sipidList.value = [];
		total.value = 0;
	} finally {
		loading.value = false;
	}
};

const cityOptions = regionData;


// 表单引用
const queryForm = ref(null);

// 查询方法
const handleQuery = () => {
	loading.value = true;
	queryParams.pageNum = 1;
	getList();
};

// 重置方法
const resetQuery = () => {
	queryParams.deviceSipId = '';
	queryParams.channelSipId = '';
	queryParams.status = '';
	handleQuery();
};

// 处理选中行
const handleSelectionChange = (selection) => {
	ids.value = selection.map((item) => item.id);
	multiple.value = !selection.length;
};

// 双击单元格
const celldblclick = (row, column, cell, event) => {
	console.log('双击单元格：', row, column, cell, event);
};

// 自定义选择逻辑
const selectable = (row, index) => {
	return !row.disabled;
};

// 切换省份事件
const changeProvince = (value) => {
	console.log('选择的省市区:', value);
	if (value && value[0] && value[1] && value[2]) {
    createForm.value.citycode =
      CodeToText[value[0]] + '/' + CodeToText[value[1]] + '/' + CodeToText[value[2]];
  }
};

const openProductDialog = () => {
        productDialogVisible.value.open = true;
};
	// 关闭产品选择弹窗
const closeProductDialog = () => {
    productDialogVisible.value = false;
};

// 选择产品
const selectProduct = () => {
	console.log('选择产品');
};

// 获取并设置产品数据
const getProductData = (product) => {
    if (!product) return;

    createForm.value.productId = product.productId;
    createForm.value.productName = product.productName;
    createForm.value.tenantId = product.tenantId;
    createForm.value.tenantName = product.tenantName;
};

// 提交创建表单
const submitForm = () => {
	console.log('提交创建表单:', createForm);
};

// 分页改变
const onHandleSizeChange = (val) => {
	queryParams.pageSize = val;
	getList();
};

// 分页改变
const onHandleCurrentChange = (val) => {
	queryParams.pageNum = val;
	getList();
};

// 取消创建表单
const cancel = () => {
	open.value = false;
};

// 处理绑定操作
const handleBinding = (row) => {
	Object.assign(form.value, row); // 深拷贝赋值
	bindingOpen.value = true;
	title.value = '监控设备绑定'; // 替换为 $t 翻译即可
};

// 提交绑定表单
const submitFormBinding = () => {
	console.log('提交绑定表单:', form.value);
};

// 取消绑定
const cancelBinding = () => {
	bindingOpen.value = false;
};

// 分页大小改变
const handleSizeChange = (val) => {
	console.log(`每页 ${val} 条`);
	queryParams.pageSize = val;
	getList();
};

// 当前页改变
const handleCurrentChange = (val) => {
	console.log(`当前页: ${val}`);
	queryParams.pageNum = val;
	getList();
};

// 查看设备详情
const handleViewDevice = (id) => {
	console.log('查看设备:', id);
};

// 添加按钮
const handleAdd = () => {
	open.value = true;
	title.value = '生成设备通道';
	console.log('添加');
};

// 删除按钮
const handleDelete = async (row) => {
    const sipIds = row.id || ids.value;

    try {
        await ElMessageBox.confirm(
            `确认删除ID为 ${sipIds} 的记录吗？`, // 也可以替换为 this.$t('xxx')
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        );

        await delChannel(sipIds); // 调用删除API
        await getList(); // 刷新列表

        ElMessage.success('删除成功'); // 替换 this.$modal.msgSuccess
    } catch (error) {
        // 用户点击取消或出现错误
        console.error('删除失败:', error);
    }
};
// 分页查询方法
// const getList = () => {
// 	loading.value = true;
// 	setTimeout(() => {
// 		loading.value = false;
// 		console.log('分页加载数据：', queryParams);
// 	}, 500);
// };

// 导入标准时间格式化函数
const parseTime = (time, format) => {
	// 如果是字符串
	if (typeof time === 'string') {
		// 只有日期没有时间
		if (/^\d{4}-\d{2}-\d{2}$/.test(time)) {
			time = time + 'T00:00:00';
		} else if (time.indexOf('T') === -1) {
			time = time.replace(' ', 'T');
		}
	}
	const date = new Date(time);
	const y = date.getFullYear();
	const m = String(date.getMonth() + 1).padStart(2, '0');
	const d = String(date.getDate()).padStart(2, '0');
	const h = String(date.getHours()).padStart(2, '0');
	const i = String(date.getMinutes()).padStart(2, '0');
	const s = String(date.getSeconds()).padStart(2, '0');

	return format.replace('{y}', y).replace('{m}', m).replace('{d}', d).replace('{h}', h).replace('{m}', i).replace('{s}', s);
};

// 获取字典数据的方法
const getdictdata = async () => {
	try {
		// 加载各个字典
		dict.type.sip_gen_status = await dictStore.fetchDict('sip_gen_status'); // 新增字段
		dict.type.iot_device_status = await dictStore.fetchDict('iot_device_status');
		dict.type.video_type = await dictStore.fetchDict('video_type');
		dict.type.channel_type = await dictStore.fetchDict('channel_type');
		
		console.log('字典数据加载完成:', {
			video_type: dict.type.video_type,
			channel_type: dict.type.channel_type
		});
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};

// 页面加载时调用
onMounted(() => {
	showSearch.value = true;
	getdictdata();
	getList();
});

// 处理搜索栏显示/隐藏
const handleShowSearch = (val) => {
    console.log('搜索栏状态改变:', val);
    showSearch.value = val;
};
</script>

<style lang="scss" scoped>
.iot-sip {
	padding: 16px;

	.el-card {
		border-radius: 8px;
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		margin-bottom: 16px;

		&:hover {
			box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
		}
	}

	.el-button {
		transition: all 0.3s;
		border-radius: 4px;

		&:not(.is-disabled):hover {
			transform: translateY(-2px);
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
		}
	}

	.el-table {
		border-radius: 8px;
		overflow: hidden;

		:deep(.el-table__row) {
			transition: background-color 0.3s;

			&:hover {
				background-color: #f5f7fa;
			}
		}

		:deep(.el-table__cell) {
			padding: 8px 0;
			border-right: none !important;
		}

		:deep(.el-table__header) {
			th {
				border-right: none !important;
			}
		}

		:deep(.el-table__body) {
			td {
				border-bottom: 1px solid #ebeef5;
			}
		}
	}

	.el-form-item {
		margin-bottom: 16px;
	}

	.el-select {
		width: 100%;
	}

	.el-link {
		font-weight: 500;
	}
}

.status-select-dropdown {
	.el-select-dropdown__item {
		padding: 0 16px;
	}
	z-index: 9999 !important;
}
</style>
