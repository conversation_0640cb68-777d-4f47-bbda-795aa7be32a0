// 变量实时数据更新回调函数类型
export type VariableUpdateCallback = (variableId: number, data: any) => void;

interface VariableRealTimeTool {
  socket: WebSocket | null;
  isConnected: boolean;
  callbacks: Map<number, VariableUpdateCallback[]>;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectInterval: number;
  connect: () => void;
  disconnect: () => void;
  subscribeVariable: (variableId: number, callback: VariableUpdateCallback) => void;
  unsubscribeVariable: (variableId: number, callback?: VariableUpdateCallback) => void;
  subscribeAllVariables: (callback: VariableUpdateCallback) => void;
  unsubscribeAllVariables: () => void;
  sendMessage: (message: any) => void;
  subscribeToTopic: (variableId: number) => void;
  handleVariableUpdate: (variableId: number, data: any) => void;
}

const variableRealTimeTool: VariableRealTimeTool = {
  socket: null,
  isConnected: false,
  callbacks: new Map(),
  reconnectAttempts: 0,
  maxReconnectAttempts: 5,
  reconnectInterval: 3000,

  // 连接WebSocket
  connect() {
    if (this.socket && this.isConnected) {
      console.log('变量实时数据连接已存在');
      return;
    }

    // 配置WebSocket地址
    let url: string = import.meta.env.VITE_WEBSOCKET_URL || '';

    if (url === '') {
      console.log('自动获取变量实时数据WebSocket连接地址');
      if (window.location.protocol === 'http:') {
        // 尝试不同的WebSocket端口和路径
        const endpoints = [
          `ws://${window.location.hostname}:5000/websocket`,
          `ws://${window.location.hostname}:8083/mqtt`,
          `ws://${window.location.hostname}:8080/websocket`,
          `ws://${window.location.hostname}:3000/websocket`
        ];
        url = endpoints[0];
      } else {
        url = `wss://${window.location.hostname}/websocket`;
      }
    }
    console.log('变量实时数据WebSocket地址：', url);

    try {
      this.socket = new WebSocket(url);

      this.socket.onopen = () => {
        console.log('变量实时数据WebSocket连接成功');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 连接成功后，发送认证信息
        this.sendMessage({
          type: 'auth',
          token: localStorage.getItem('token') || ''
        });
        
        // 重新订阅所有已注册的变量
        this.callbacks.forEach((callbacks, variableId) => {
          if (callbacks.length > 0) {
            this.subscribeToTopic(variableId);
          }
        });
      };

      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('收到变量实时数据更新:', data);
          
          // 处理不同类型的消息
          if (data.type === 'variable_update') {
            this.handleVariableUpdate(data.variableId, data.data);
          } else if (data.type === 'heartbeat') {
            // 心跳响应
            this.sendMessage({ type: 'heartbeat_response' });
          } else if (data.type === 'error') {
            console.error('WebSocket错误:', data.message);
          }
        } catch (error) {
          console.error('处理WebSocket消息失败:', error);
        }
      };

      this.socket.onclose = (event) => {
        console.log('变量实时数据WebSocket连接已断开:', event.code, event.reason);
        this.isConnected = false;
        
        // 尝试重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++;
          console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
          setTimeout(() => {
            this.connect();
          }, this.reconnectInterval);
        } else {
          console.log('达到最大重连次数，停止重连');
        }
      };

      this.socket.onerror = (error) => {
        console.error('变量实时数据WebSocket连接错误:', error);
        this.isConnected = false;
      };

    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      this.isConnected = false;
    }
  },

  // 断开连接
  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.isConnected = false;
      this.reconnectAttempts = 0;
      console.log('变量实时数据WebSocket连接已断开');
    }
  },

  // 发送消息
  sendMessage(message: any) {
    if (this.socket && this.isConnected) {
      try {
        this.socket.send(JSON.stringify(message));
      } catch (error) {
        console.error('发送WebSocket消息失败:', error);
      }
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  },

  // 订阅特定变量的主题
  subscribeToTopic(variableId: number) {
    if (!this.isConnected) {
      console.log('WebSocket未连接，无法订阅变量主题');
      return;
    }

    this.sendMessage({
      type: 'subscribe',
      topic: `variable/realtime/${variableId}`
    });
  },

  // 处理变量更新
  handleVariableUpdate(variableId: number, data: any) {
    const callbacks = this.callbacks.get(variableId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(variableId, data);
        } catch (error) {
          console.error('变量更新回调执行失败:', error);
        }
      });
    }
  },

  // 订阅特定变量的实时数据
  subscribeVariable(variableId: number, callback: VariableUpdateCallback) {
    // 注册回调函数
    if (!this.callbacks.has(variableId)) {
      this.callbacks.set(variableId, []);
    }
    
    const callbacks = this.callbacks.get(variableId)!;
    if (!callbacks.includes(callback)) {
      callbacks.push(callback);
    }

    // 如果已连接，立即订阅主题
    if (this.isConnected) {
      this.subscribeToTopic(variableId);
    } else {
      // 如果未连接，先连接
      this.connect();
    }
  },

  // 取消订阅特定变量的实时数据
  unsubscribeVariable(variableId: number, callback?: VariableUpdateCallback) {
    const callbacks = this.callbacks.get(variableId);
    if (!callbacks) {
      return;
    }

    if (callback) {
      // 移除特定的回调函数
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      // 移除所有回调函数
      callbacks.length = 0;
    }

    // 如果没有回调函数了，取消订阅主题
    if (callbacks.length === 0) {
      this.callbacks.delete(variableId);
      
      if (this.isConnected) {
        this.sendMessage({
          type: 'unsubscribe',
          topic: `variable/realtime/${variableId}`
        });
      }
    }
  },

  // 订阅所有变量的实时数据
  subscribeAllVariables(callback: VariableUpdateCallback) {
    if (this.isConnected) {
      this.sendMessage({
        type: 'subscribe',
        topic: 'variable/realtime/+'
      });
      // 注册全局回调
      this.callbacks.set(-1, [callback]);
    } else {
      this.connect();
      // 连接成功后会自动订阅
      this.callbacks.set(-1, [callback]);
    }
  },

  // 取消订阅所有变量的实时数据
  unsubscribeAllVariables() {
    if (this.isConnected) {
      this.sendMessage({
        type: 'unsubscribe',
        topic: 'variable/realtime/+'
      });
    }
    this.callbacks.clear();
  }
};

export default variableRealTimeTool;
