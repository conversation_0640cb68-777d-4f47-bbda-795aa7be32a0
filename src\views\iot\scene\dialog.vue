<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="800">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="脚本名称" prop="scriptName">
                            <el-input v-model="state.ruleForm.scriptName" placeholder="请输入脚本名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="执行顺序" prop="scriptOrder">
                            <el-input-number v-model="state.ruleForm.scriptOrder" placeholder="请输入脚本名" type="number"
                                controls-position="right" style="width: 100%" />
                        </el-form-item>
                    </el-col>

                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="脚本事件" prop="scriptEvent">
                            <el-select v-model="state.ruleForm.scriptEvent" placeholder="请选择脚本事件" style="width: 100%">
                                <el-option v-for="dict in script_event_list" :key="dict.dictLabel"
                                    :label="dict.dictLabel" :value="Number(dict.dictValue)"
                                    :disabled="dict.dictValue !== '1' && dict.dictValue !== '2'"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="脚本动作" prop="scriptAction">
                            <el-select v-model="state.ruleForm.scriptAction" placeholder="请选择脚本动作" style="width: 100%">
                                <el-option v-for="dict in script_action_list" :key="dict.dictLabel"
                                    :label="dict.dictLabel" :value="Number(dict.dictValue)"
                                    :disabled="dict.dictValue !== '1'"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="脚本状态" prop="enable">
                            <el-switch v-model="state.ruleForm.enable" :active-value="1" :inactive-value="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="所属产品" prop="productName">
                            <el-input readonly v-model="state.ruleForm.productName" placeholder="请选择产品"
                                style="margin-top: 3px">
                                <template #append>
                                    <el-button @click="handleSelectProduct()">选择产品</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" style="float: right"></el-col>
                </el-row>
            </el-form>
            <div style="padding: 0px 10px">
                <AceEditor ref="codeEditor" v-model="state.ruleForm.scriptData" lang="'groovy'" :codeStyle="'chrome'"
                    :readonly="false"></AceEditor>
            </div>
            <div style="padding: 0 10px; margin: 10px 0">
                <el-alert :title="state.validateMsg" type="success" show-icon
                    v-if="state.isValidate && state.validateMsg" :closable="false"></el-alert>
                <el-alert :title="state.validateMsg" type="error" show-icon
                    v-if="!state.isValidate && state.validateMsg" :closable="false"></el-alert>
            </div>
            <template #footer>
                <span class="dialog-footer" style="float: left">
                    <el-link style="line-height: 40px; padding-left: 20px" underline="never" type="primary"
                        href="https://fastbee.cn/doc/pages/rule_engine/" target="_blank">
                        <el-icon><ele-QuestionFilled /></el-icon> 脚本使用Groovy引擎，查看教程>>>
                    </el-link>
                </span>
                <el-button type="success" @click="handleValidate">验 证</el-button>
                <el-button type="primary" @click="onSubmit(DialogFormRef)" v-auths="['iot:script:edit']"
                    v-show="state.ruleForm.scriptId" :disabled="!state.isValidate">修
                    改</el-button>
                <el-button type="primary" @click="onSubmit(DialogFormRef)" v-auths="['iot:script:add']"
                    v-show="!state.ruleForm.scriptId" :disabled="!state.isValidate">新
                    增</el-button>
                <el-button @click="onCancel">取 消</el-button>
            </template>
        </el-dialog>
        <!-- 产品列表 -->
        <ProductList ref="ProductListRef" @productEvent="getSelectProduct" />
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { defineAsyncComponent, nextTick, reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addNewsCategory, getNewsCategory, updateNewsCategory } from '/@/api/iot/newsCategory';
const dictStore = useDictStore();  // 使用 Pinia store
import AceEditor from '/@/views/iot/scene/components/editor/editor.vue';
import { addScript, getScript, updateScript, validateScript } from '/@/api/iot/script';
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
// 引入组件
const ProductList = defineAsyncComponent(() => import('/@/views/iot/scene/product-list.vue'));

// 定义变量内容
const ProductListRef = ref();
const initialState = {
    ruleForm: {
        id: null,
        applicationName: 'fastbee', // 后端配置和规则/脚本需要一致
        productId: null,
        scriptName: null,
        scriptType: 'script',
        remark: null,
        scriptId: '',
        productName: '',
        scriptLanguage: 'groovy',
        enable: 1,
        scriptPurpose: 1, // 脚本用途(1=数据流，2=触发器，3=执行动作)
        scriptOrder: 1, // 脚本执行顺序，数字越大越先执行
        scriptAction: 1, // 脚本动作(1=消息重发，2=消息通知，3=Http推送，4=Mqtt桥接，5=数据库存储)
        scriptEvent: 1, // 脚本事件(1=设备上报，2=平台下发，3=设备上线，4=设备离线)
        sceneId: 0,
        scriptData: `import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.NumberUtil;

// 1. 获取主题和内容(必要)
String topic = msgContext.getTopic();
String payload = msgContext.getPayload();


// 2. 数据转换(自己处理)
println ("根据情况转换数据")
String NewTopic = topic;
String NewPayload = payload;


// 3. 返回新的数据（必要）
msgContext.setTopic(NewTopic);
msgContext.setPayload(NewPayload);`
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
    isValidate: false,
    validateMsg: '',
}
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const script_action_list = ref<TypeOption[]>([]);
const script_event_list = ref<TypeOption[]>([]);
// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
    isValidate: initialState.isValidate,
    validateMsg: initialState.validateMsg,
});

// 校验规则
const rules = reactive({
    scriptId: [{ required: true, message: '脚本标识不能为空', trigger: 'blur' }],
    productName: [{ required: true, message: '所属产品不能为空', trigger: 'blur' }],
    scriptName: [{ required: true, message: '脚本名不能为空', trigger: 'blur' }],
    scriptType: [{ required: true, message: '脚本类型不能为空', trigger: 'change' }],
    scriptLanguage: [{ required: true, message: '脚本语言不能为空', trigger: 'change' }],
    scriptEvent: [{ required: true, message: '', trigger: 'change' }],
    scriptAction: [{ required: true, message: '', trigger: 'change' }],
    scriptOrder: [{ required: true, message: '', trigger: 'change' }],
    enable: [{ required: true, message: '状态不能为空', trigger: 'blur' }],

})
/** 验证按钮操作 */
const handleValidate = () => {
    state.validateMsg = '';
    state.isValidate = false;
    validateScript(state.ruleForm).then((response) => {
        state.isValidate = response.data.data;
        state.validateMsg = response.data.msg;
    });
}
// 打开弹窗
const openDialog = async (type: string, row: any, scriptId: string) => {
    resetState();
    if (type == 'edit') {
        if (row != undefined) {
            const response = await getScript(row.scriptId)
            state.ruleForm = response.data.data

        } else {
            const response = await getScript(scriptId)
            state.ruleForm = response.data.data
        }
        state.dialog.title = '修改新闻分类';
        state.dialog.submitTxt = '修 改';
    } else {
        state.dialog.title = '新增新闻分类';
        state.dialog.submitTxt = '新 增';
    }

    getdictdata()
    state.dialog.isShowDialog = true;

};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.scriptId != '') {
                updateScript(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {
                addScript(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
    state.isValidate = false
    state.validateMsg = ''
};
//打开选择产品弹窗
const handleSelectProduct = () => {
    ProductListRef.value.openProductList();
}
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 获取状态数据
const getdictdata = async () => {
    try {
        script_event_list.value = await dictStore.fetchDict('rule_script_event')
        script_action_list.value = await dictStore.fetchDict('rule_script_action')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
const getSelectProduct = (product: any) => {
    state.ruleForm.productName = product.productName;
    state.ruleForm.productId = product.productId;
}


// 暴露变量
defineExpose({
    openDialog,
});
</script>
