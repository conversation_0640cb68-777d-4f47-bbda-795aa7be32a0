<template>
    <div style="padding-left: 20px">
        <el-form size="default" :inline="true" label-width="68px">
            <el-form-item label="设备编号" prop="serialNumber">
                <el-input v-model="state.tableData.queryParams.serialNumber" placeholder="请输入设备编号" clearable
                    size="default" />
            </el-form-item>
            <el-form-item label="授权码" prop="authorizeCode">
                <el-input v-model="state.tableData.queryParams.authorizeCode" placeholder="请输入授权码" clearable
                    size="default" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-select style="width: 150px;" v-model="state.tableData.queryParams.status" placeholder="请选择状态"
                    clearable size="default">
                    <el-option v-for="dict in auth_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button size="default" type="primary" class="ml10" @click="getTableData">
                    <el-icon>
                        <ele-Search />
                    </el-icon>
                    查询
                </el-button>
                <el-button size="default" @click="resetQuery">
                    <el-icon><ele-Refresh /></el-icon>
                    重置
                </el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8" :justify="'space-between'">
            <div>
                <el-button v-auths="['iot:authorize:add']" size="default" plain type="primary" class="ml5"
                    @click="handleAdd">
                    <el-icon><ele-Plus /></el-icon>
                    生成授权码
                </el-button>
                <el-button v-auths="['iot:authorize:remove']" size="default" plain type="danger" :disabled="multiple"
                    class="ml10" @click="handleDelete">
                    <el-icon><ele-DeleteFilled /></el-icon>
                    批量删除
                </el-button>
                <el-button v-auths="['iot:authorize:export']" size="default" plain type="warning" class="ml10"
                    @click="handleExport">
                    <el-icon><ele-Download /></el-icon>
                    导出
                </el-button>
                <el-link type="info" style="padding-top:5px;margin-left: 10px;"
                    underline="never">Tips：双击可以复制授权码。</el-link>
            </div>
        </el-row>


        <el-table v-loading="state.tableData.loading" :data="state.tableData.authorizeList" border style="width: 100%"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" @selection-change="handleSelectionChange"
            @cell-dblclick="celldblclick">
            <el-table-column type="selection" :selectable="selectable" width="55" align="center" />
            <el-table-column label="授权码" width="320" align="center" prop="authorizeCode" />
            <el-table-column label="状态" align="center" prop="active" width="100">
                <template #default="scope">
                    <dictTag :options="auth_status_list" :value="scope.row.status" />
                </template>
            </el-table-column>
            <el-table-column label="设备编号" width="150" align="center" prop="serialNumber">
                <template #default="scope">
                    <el-link type="primary" @click="getdeviceBySerialNumber(scope.row.serialNumber)"
                        underline="never">{{ scope.row.serialNumber }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="授权时间" align="center" prop="updateTime" width="180">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" align="center" prop="remark" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button size="small" text @click="handleUpdate(scope.row, 'auth')" type="primary"
                        v-auths="['iot:authorize:edit']"
                        v-if="scope.row.status == 1 && !scope.row.deviceId"><el-icon><ele-UserFilled /></el-icon>设备授权</el-button>
                    <el-button size="small" text @click="handleUpdate(scope.row, 'remark')" type="primary"
                        v-auths="['iot:authorize:edit']"><el-icon><ele-View /></el-icon>备注</el-button>
                    <el-button size="small" text @click="handleDelete(scope.row)" type="primary"
                        v-auths="['iot:authorize:remove']"
                        v-if="!scope.row.deviceId"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
            style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
            v-model:current-page="state.tableData.queryParams.pageNum" background
            v-model:page-size="state.tableData.queryParams.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <!-- 设备授权和授权备注对话框 -->
        <el-dialog :title="devicestate.dialog.title" v-model="devicestate.dialog.isShowDialog"
            :width="devicestate.dialog.editWidth" style="position: absolute; top: 100px;" append-to-body>
            <div v-if="devicestate.dialog.editType == 'auth'">
                <div class="el-divider el-divider--horizontal" style="margin-top: -25px;"></div>
                <el-form :model="devicestate.tableData.deviceParams" ref="queryDeviceForm" :inline="true"
                    label-width="68px">
                    <el-form-item label="设备名称" prop="deviceName">
                        <el-input v-model="devicestate.tableData.deviceParams.deviceName" placeholder="请输入设备名称"
                            clearable size="default" style="width:150px;" />
                    </el-form-item>
                    <el-form-item label="设备编号" prop="serialNumber">
                        <el-input v-model="devicestate.tableData.deviceParams.serialNumber" placeholder="请输入设备编号"
                            clearable size="default" style="width:150px;" />
                    </el-form-item>

                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="handleDeviceQuery">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetDeviceQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>

                </el-form>
                <el-table v-loading="devicestate.tableData.loading" :data="devicestate.tableData.deviceList"
                    ref="singleTable" @row-click="rowClick" highlight-current-row border style="width: 100%"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column label="选择" width="70" align="center">
                        <template #default="scope">
                            <input type="radio" :checked="scope.row.isSelect" name="device" />
                        </template>
                    </el-table-column>
                    <el-table-column label="设备名称" align="center" prop="deviceName" />
                    <el-table-column label="设备ID" align="center" prop="deviceId" />
                    <el-table-column label="设备编号" align="center" prop="serialNumber" width="200" />
                    <el-table-column label="用户名称" align="center" prop="userName" />
                    <el-table-column label="设备状态" align="center" prop="status">
                        <template #default="scope">
                            <dictTag :options="device_status_list" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination class="mt15" style="justify-content: flex-end;" @size-change="ondeviceHandleSizeChange" @current-change="ondeviceHandleCurrentChange"
                    v-show="devicestate.tableData.deviceTotal > 0" :total="devicestate.tableData.deviceTotal"
                    v-model:current-page="devicestate.tableData.deviceParams.pageNum" background size="small"
                    layout="total, sizes, prev, pager, next, jumper" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:page-size="devicestate.tableData.deviceParams.pageSize" />
            </div>
            <div v-if="devicestate.dialog.editType == 'remark'">
                <el-input v-model="devicestate.ruleForm.remark" type="textarea" rows="4" placeholder="请输入内容" />
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="submitForm" size="default">{{
                        devicestate.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 设备详情对话框 -->
        <el-dialog title="设备详情" v-model="openDevice" width="600px" append-to-body
            style="position: absolute; top: 100px;">
            <div v-if="device == null" style="text-align:center;"><i class="el-icon-warning" style="color:#E6A23C;"></i>
                提示：查找不到设备，可能已经被删除</div>
            <el-descriptions border :column="2" size="medium" v-if="device != null">
                <el-descriptions-item label="设备ID">{{ device.deviceId }}</el-descriptions-item>
                <el-descriptions-item label="设备名称">{{ device.deviceName }}</el-descriptions-item>
                <el-descriptions-item label="设备编号">{{ device.serialNumber }}</el-descriptions-item>
                <el-descriptions-item label="设备状态">
                    <!-- （1-未激活，2-禁用，3-在线，4-离线） -->
                    <el-tag v-if="device.status == 1" type="warning">未激活</el-tag>
                    <el-tag v-else-if="device.status == 2" type="danger">禁用</el-tag>
                    <el-tag v-else-if="device.status == 3" type="success">在线</el-tag>
                    <el-tag v-else-if="device.status == 4" type="info">离线</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="设备影子">
                    <el-tag v-if="device.isShadow == 1" type="success">启用</el-tag>
                    <el-tag v-else type="info">未启用</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="定位方式">
                    <!-- (1=ip自动定位，2=设备定位，3=自定义) -->
                    <el-tag v-if="device.locationWay == 1" type="success">自动定位</el-tag>
                    <el-tag v-else-if="device.locationWay == 2" type="warning">设备定位</el-tag>
                    <el-tag v-else-if="device.locationWay == 3" type="primary">自定义位置</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="产品名称">{{ device.productName }}</el-descriptions-item>
                <el-descriptions-item label="租户名称">{{ device.userName }}</el-descriptions-item>
                <el-descriptions-item label="固件版本">Version {{ device.firmwareVersion }}</el-descriptions-item>
                <el-descriptions-item label="所在地址">{{ device.networkAddress }}</el-descriptions-item>
                <el-descriptions-item label="设备经度">{{ device.longitude }}</el-descriptions-item>
                <el-descriptions-item label="设备纬度">{{ device.latitude }}</el-descriptions-item>
                <el-descriptions-item label="入网IP">{{ device.networkIp }}</el-descriptions-item>
                <el-descriptions-item label="设备信号">{{ device.rssi }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ device.createTime }}</el-descriptions-item>
                <el-descriptions-item label="激活时间">{{ device.activeTime }}</el-descriptions-item>
                <el-descriptions-item label="备注信息">{{ device.remark }}</el-descriptions-item>
            </el-descriptions>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="openDevice = false;" size="default">关 闭</el-button>
                    <el-button type="primary" @click="goToEditDevice(device.deviceId)" size="default">查看设备</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>


<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import { parseTime } from '/@/utils/next'
import { addProductAuthorizeByNum, delAuthorize, getAuthorize, listAuthorize, updateAuthorize } from '/@/api/iot/authorize';
import { download } from '/@/utils/request';
import { getDeviceBySerialNumber } from '/@/api/iot/device';
import router from '/@/router';
const dictStore = useDictStore();  // 使用 Pinia store
// 引入组件
// 定义变量内容
// 定义 props
const props = defineProps({
    modelValue: {
        type: Object
    }
});
// 授权码列表
const state = reactive({
    tableData: {
        authorizeList: [],
        total: 0,
        loading: false,
        queryParams: {
            authorizeCode: '' as any,
            serialNumber: '' as any,
            pageNum: 1,
            pageSize: 10,
            status: '' as any,
            productId: null,
            productName: null
        },

    },
});
interface deviceData {
    isSelect: any,
    deviceId: any

}
// 设备授权列表
const devicestate = reactive({
    tableData: {
        deviceList: [] as deviceData[],
        deviceTotal: 0,
        loading: false,
        deviceParams: {
            deviceName: '' as any,
            serialNumber: '' as any,
            pageNum: 1,
            pageSize: 10,
            // status: '' as any,
            productId: null,
            // productName: null
        },
    },
    ruleForm: {
        authorizeId: null as any,
        authorizeCode: null,
        productId: "",
        userId: "",
        deviceId: null as any,
        serialNumber: null as any,
        userName: null as any,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
    },
    dialog: {
        isShowDialog: false,
        title: '',
        submitTxt: '确 定',
        editWidth: '500px',
        editType: ''
    },
});
const device = ref({
    deviceId: null as any,
    status: null as any,
    deviceName: null as any,
    serialNumber: null as any,
    isShadow: null as any,
    productName: null as any,
    userName: null as any,
    firmwareVersion: null as any,
    networkAddress: null as any,
    longitude: null as any,
    latitude: null as any,
    networkIp: null as any,
    rssi: null as any,
    createTime: null as any,
    activeTime: null as any,
    remark: null as any,
    locationWay: null as any
})
//接收父组件得产品信息
const productInfo = ref({
    status: '' as any,
    productId: '' as any,
    productName: '' as any,
    isModbus: false,
})

interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const auth_status_list = ref<TypeOption[]>([]);
const device_status_list = ref<TypeOption[]>([]);
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //authorizeId
const createNum = ref(10) //// 新增授权码个数
const openDevice = ref(false) //是否显示设备详情
// 监听props的变化
watch(() => props.modelValue, (newVal) => {
    try {
        productInfo.value = newVal as any
        if (productInfo.value && productInfo.value.productId != 0) {
            state.tableData.queryParams.productId = productInfo.value.productId;
            devicestate.tableData.deviceParams.productId = productInfo.value.productId;
            // state.tableData.queryParams.productName = productInfo.value.productName;
            getTableData();
            getdictdata()
        }
    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
/** 查询产品物模型列表 */
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        const response = await listAuthorize(state.tableData.queryParams);
        state.tableData.total = response.data.total;
        state.tableData.authorizeList = response.data.rows;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
/** 查询产品物模型列表 */
const getDeviceList = async () => {
    try {
        devicestate.tableData.loading = true;
        devicestate.tableData.deviceParams = {
            deviceName: '' as any,
            serialNumber: '' as any,
            pageNum: 1,
            pageSize: 10,
            productId: productInfo.value.productId
        }
        // const response = await listUnAuthDevice(devicestate.tableData.deviceParams)
        // for (let i = 0; i < response.data.rows.length; i++) {
        //     response.data.rows[i].isSelect = false;
        // }
        // devicestate.tableData.deviceTotal = response.data.total;
        // devicestate.tableData.deviceList = response.data.rows;
        console.log(devicestate.tableData.deviceList);

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            devicestate.tableData.loading = false;
        }, 500);
    }
}
/**设备授权搜索按钮操作 */
const handleDeviceQuery = () => {
    devicestate.tableData.deviceParams.pageNum = 1;
    getDeviceList();
}
/** 设备授权重置按钮操作 */
const resetDeviceQuery = () => {
    devicestate.tableData.deviceParams = {
        deviceName: '' as any,
        serialNumber: '' as any,
        pageNum: 1,
        pageSize: 10,
        productId: productInfo.value.productId,
    }
    handleDeviceQuery();
}
/** 单选数据 */
const rowClick = (device: { deviceId: string; userId: string; userName: string; serialNumber: string; } | null) => {
    if (device != null) {
        setRadioSelected(device.deviceId);
        // 赋值
        devicestate.ruleForm.userId = device.userId;
        devicestate.ruleForm.userName = device.userName;
        devicestate.ruleForm.deviceId = device.deviceId;
        devicestate.ruleForm.serialNumber = device.serialNumber;
    }
}
/** 设置单选按钮选中 */
const setRadioSelected = (deviceId: string) => {
    for (let i = 0; i < devicestate.tableData.deviceList.length; i++) {
        let device = devicestate.tableData.deviceList[i];
        if (devicestate.tableData.deviceList[i].deviceId == deviceId) {
            device.isSelect = true;
            devicestate.tableData.deviceList[i] = device
        } else {
            device.isSelect = false;
            devicestate.tableData.deviceList[i] = device
        }
    }
}
// 取消按钮
const cancel = () => {
    devicestate.tableData.loading = false;
    reset();
}
// 表单重置
const reset = () => {
    devicestate.ruleForm = {
        authorizeId: null as any,
        authorizeCode: null,
        productId: "",
        userId: "",
        deviceId: null as any,
        serialNumber: null as any,
        userName: null as any,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
    };
    device.value = {
        deviceId: null as any,
        status: null as any,
        deviceName: null as any,
        serialNumber: null as any,
        isShadow: null as any,
        productName: null as any,
        userName: null as any,
        firmwareVersion: null as any,
        networkAddress: null as any,
        longitude: null as any,
        latitude: null as any,
        networkIp: null as any,
        rssi: null as any,
        createTime: null as any,
        activeTime: null as any,
        remark: null as any,
        locationWay: null as any
    };
}
/** 提交按钮 */
const submitForm = () => {
    if (devicestate.dialog.editType == 'auth') {
        if (devicestate.ruleForm.deviceId != null && devicestate.ruleForm.deviceId != 0) {
            updateAuthorize(devicestate.ruleForm).then(response => {
                ElMessage.success("设备授权成功");
                devicestate.dialog.isShowDialog = false;
                getTableData();
            });
        } else {
            ElMessage.error("请选择要授权的设备");
        }
    } else if (devicestate.ruleForm.authorizeId != null) {
        updateAuthorize(devicestate.ruleForm).then(response => {
            ElMessage.success("备注成功");
            devicestate.dialog.isShowDialog = false;
            getTableData();
        });
    }
}
/**获取设备详情*/
const getdeviceBySerialNumber = (serialNumber: string) => {
    openDevice.value = true;
    getDeviceBySerialNumber(serialNumber).then(response => {
        device.value = response.data.data;
    });
}
/** 查看设备按钮操作 */
const goToEditDevice = (deviceId: any) => {
    openDevice.value = false;
    router.push({
        path: '/iot/device-edit',
        query: {
            deviceId: deviceId,
        }
    });
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.queryParams = {
        authorizeCode: '' as any,
        serialNumber: '' as any,
        pageNum: 1,
        pageSize: 10,
        status: '' as any,
        productId: null,
        productName: null
    }
    getTableData()
}
// 获取状态数据
const getdictdata = async () => {
    try {
        auth_status_list.value = await dictStore.fetchDict('iot_auth_status')
        device_status_list.value = await dictStore.fetchDict('iot_device_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item) => item.authorizeId);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}
/** 新增按钮操作 */
const handleAdd = () => {
    ElMessageBox.prompt('', '输入授权码数量', {
        customClass: 'createNum',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /[0-9\-]/,
        inputErrorMessage: '数量内容不正确',
        inputType: 'number',
        inputValue: createNum.value as any
    })
        .then(({ value }) => {
            createNum.value = value as any
            if (state.tableData.queryParams.productId != null) {
                let addData = {
                    productId: state.tableData.queryParams.productId,
                    createNum: createNum.value
                }
                addProductAuthorizeByNum(addData).then(response => {
                    ElMessage.success("新增授权码成功");
                    getTableData();
                    createNum.value = 10;
                });
            }
            ElMessage({
                type: 'success',
                message: `Your email is:${value}`,
            })
        })
        .catch(() => {
            ElMessage({
                type: 'info',
                message: '取消新增',
            })
        })
}

/** 修改按钮操作 */
const handleUpdate = (row: { authorizeId: any; }, editType: string) => {
    reset();
    devicestate.dialog.editType = editType;
    const authorizeId = row.authorizeId || ids.value
    getAuthorize(authorizeId).then(response => {
        devicestate.ruleForm = response.data.data;
        devicestate.dialog.isShowDialog = true;
        if (devicestate.dialog.editType == 'auth') {
            devicestate.dialog.title = "选择设备";
            devicestate.dialog.editWidth = "800px";
        } else {
            devicestate.dialog.title = "备注信息";
            devicestate.dialog.editWidth = "500px";
        }
        // 取消选中
        getDeviceList()
        for (let i = 0; i < devicestate.tableData.deviceList.length; i++) {
            let device = devicestate.tableData.deviceList[i];
            device.isSelect = false;
            devicestate.tableData.deviceList[i] = device
        }
    });
}
/** 删除按钮操作 */
const handleDelete = (row: { authorizeId: any; }) => {
    const authorizeIds = row.authorizeId || ids.value

    ElMessageBox.confirm('是否确认删除产品授权码编号为"' + authorizeIds + '"的数据项？', '系统提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delAuthorize(authorizeIds).then(res => {
                ElMessage.success('删除成功');
                getTableData()
            });

        })
        .catch(() => { });

}
/** 导出按钮操作 */
const handleExport = () => {
    download('iot/authorize/export', {
        ...state.tableData.queryParams
    }, `authorize_${new Date().getTime()}.xlsx`)
}
//禁用有绑定设备的复选框
const selectable = (row: { deviceId: null; }) => {
    return row.deviceId != null ? false : true;
}
//表格增加复制功能
const celldblclick = (row: { [x: string]: string; }, column: { property: string | number; }, cell: any, event: any) => {
    navigator.clipboard.writeText(row[column.property]).then(e => {
        onCopy()
    }, function (e) {
        onError()
    })
}
const onCopy = () => {
    ElNotification({
        title: '成功',
        message: '复制成功！',
        type: 'success',
        offset: 50,
        duration: 2000
    })

}
const onError = () => {
    ElNotification({
        title: '失败',
        message: '复制失败！',
        type: 'error',
        offset: 50,
        duration: 2000
    })

}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.queryParams.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.queryParams.pageNum = val;
    getTableData();
};
// 分页改变
const ondeviceHandleSizeChange = (val: number) => {
    devicestate.tableData.deviceParams.pageSize = val;
    getDeviceList();
};
// 分页改变
const ondeviceHandleCurrentChange = (val: number) => {
    devicestate.tableData.deviceParams.pageNum = val;
    getDeviceList();
};

</script>
<style scoped>
.specsColor {
    background-color: #fcfcfc;
}
</style>