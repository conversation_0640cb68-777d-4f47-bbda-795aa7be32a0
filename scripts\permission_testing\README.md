# 权限测试脚本集合

这个目录包含了用于测试和诊断权限问题的脚本工具。

## 📋 **脚本说明**

### **1. test_getinfo_api.py**
**用途**：测试Java后端的/getInfo接口，验证用户权限数据
**使用场景**：
- 检查用户是否获得了正确的权限列表
- 验证权限数据传递是否正常
- 排查权限配置问题

**使用方法**：
```bash
python test_getinfo_api.py
```

### **2. test_java_routers.py**
**用途**：测试Java后端的/getRouters接口，验证菜单路由数据
**使用场景**：
- 检查用户能访问哪些菜单
- 验证菜单权限配置
- 排查菜单显示问题

**使用方法**：
```bash
python test_java_routers.py
```

### **3. check_thingsmodel_permissions.py**
**用途**：检查数据库中的权限标识与Java代码中的权限要求是否匹配
**使用场景**：
- 排查权限标识不匹配问题
- 验证权限配置的一致性
- 分析权限相关的403错误

**使用方法**：
```bash
python check_thingsmodel_permissions.py
```

## 🔧 **使用前准备**

1. **配置JWT Token**：
   在脚本中更新有效的JWT token
   ```python
   token = 'your_jwt_token_here'
   ```

2. **确认服务地址**：
   确保本地前端代理正常运行（http://localhost）

3. **安装依赖**：
   ```bash
   pip install aiohttp sqlalchemy
   ```

## 📝 **案例：通用物模型权限问题**

这些脚本成功帮助诊断和解决了通用物模型权限问题：

1. **问题现象**：pythontest用户访问通用物模型页面显示"权限不足"
2. **诊断过程**：
   - 使用`test_getinfo_api.py`确认用户有完整权限
   - 使用`test_java_routers.py`确认菜单配置正确
   - 使用`check_thingsmodel_permissions.py`发现权限标识不匹配
3. **问题根因**：Java API使用`iot:thingsModel:*`，但数据库配置的是`iot:template:*`
4. **解决方案**：修改Java代码中的权限标识

## 🎯 **最佳实践**

1. **系统性诊断**：按顺序使用这些脚本，全面分析权限链路
2. **保存测试结果**：将测试输出保存到文件，便于对比分析
3. **定期验证**：在权限配置变更后，使用这些脚本验证
4. **扩展脚本**：根据新的需求，可以基于这些脚本开发更多测试工具

## 📚 **相关文档**

- [用户权限配置问题诊断分析.md](../../用户权限配置问题诊断分析.md)
- [权限控制系统调研报告.md](../../权限控制系统调研报告.md)
