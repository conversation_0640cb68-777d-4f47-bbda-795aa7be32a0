#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定的FastAPI启动脚本
不使用文件监控，避免自动重启问题
"""
import sys
import traceback
import uvicorn

def main():
    try:
        print("正在导入FastAPI应用...")
        from backend.main import app
        print("✅ FastAPI应用导入成功")

        print("正在启动服务器...")
        # 生产模式启动，不监控文件变化
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,  # 关闭自动重载
            log_level="info",
            access_log=True,
            workers=1  # 单进程模式，便于调试
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
