name: ci

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  lint:
    runs-on: ubuntu-latest
    name: lint ${{ matrix.python-version }}
    strategy:
      matrix:
        python-version: [ '3.10', '3.11', '3.12', '3.13' ]
      fail-fast: false
    steps:
      - uses: https://gitea.com/actions/checkout@v4

      - name: Install uv manually with mirror
        run: |
          # 使用清华大学镜像站安装 uv
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
          
          # 全面配置国内源
          uv config set install.sources '["https://pypi.tuna.tsinghua.edu.cn/simple", "https://pypi.org/simple"]'
          uv config set install.index-url 'https://pypi.tuna.tsinghua.edu.cn/simple'
          uv config set priority 'pypi.tuna.tsinghua.edu.cn'
          
          # 可选：配置其他镜像源
          uv config set cache-dir ~/.cache/uv
          uv config set pip.index-url 'https://pypi.tuna.tsinghua.edu.cn/simple'
          
          # 显示配置信息确认
          uv config list

      - name: Verify mirror configuration
        run: |
          uv config list
          uv python list
          echo "UV_INDEX_URL=$UV_INDEX_URL"

      - name: Set up Python ${{ matrix.python-version }}
        run: uv python install ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          uv sync --only-group lint

      - name: Run lint
        run: |
          source .venv/bin/activate
          chmod 755 backend/scripts/lint.sh
          ./backend/scripts/lint.sh
