<template>
  <div class="device-container">
    <div class="table-header">
      <h2>设备列表</h2>
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        新增设备
      </el-button>
    </div>

    <el-table
      :data="tableData"
      stripe
      border
      style="width: 100%"
      v-loading="loading"
    >
      <!-- 行号 -->
      <el-table-column
        type="index"
        label="行号"
        width="80"
        align="center"
      />

      <!-- 名称 -->
      <el-table-column
        prop="name"
        label="名称"
        min-width="160"
        show-overflow-tooltip
      />

      <!-- 描述 -->
      <el-table-column
        prop="description"
        label="描述"
        min-width="180"
        show-overflow-tooltip
      />

      <!-- 通道 -->
      <el-table-column
        prop="channelName"
        label="通道"
        min-width="140"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ row.channelName || row.channelId || '-' }}
        </template>
      </el-table-column>

      <!-- 设备使能 -->
      <el-table-column
        prop="enable"
        label="设备使能"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="row.enable ? 'success' : 'info'">
            {{ row.enable ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 活跃时间 -->
      <el-table-column
        prop="activeTime"
        label="活跃时间"
        min-width="180"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          {{ formatDateTime(row.activeTime) }}
        </template>
      </el-table-column>

      <!-- 设备状态 -->
      <el-table-column
        prop="deviceStatus"
        label="设备状态"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="getStatusTag(row)">
            {{ getStatusText(row) }}
          </el-tag>
        </template>
      </el-table-column>

      <!-- 变量数量 -->
      <el-table-column
        prop="variableCount"
        label="变量数量"
        width="120"
        align="center"
      />

      <!-- 插件名称 -->
      <el-table-column
        prop="pluginName"
        label="插件名称"
        min-width="220"
        show-overflow-tooltip
      />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增设备弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="新建窗口"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-tabs v-model="activeTab" class="device-tabs">
        <el-tab-pane label="设备信息" name="device">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="140px"
            class="device-form"
          >
            <!-- 基础信息 -->
            <div class="form-section">
              <h3 class="section-title">基础信息</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="名称" prop="name">
                    <el-input
                      v-model="formData.name"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="描述" prop="description">
                    <el-input
                      v-model="formData.description"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备使能" prop="enable">
                    <el-switch
                      v-model="formData.enable"
                      active-text="启用"
                      inactive-text="禁用"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="日志等级" prop="logLevel">
                    <el-select
                      v-model="formData.logLevel"
                      placeholder="请选择..."
                      style="width: 100%"
                    >
                      <el-option label="Trace" value="Trace" />
                      <el-option label="Debug" value="Debug" />
                      <el-option label="Info" value="Info" />
                      <el-option label="Warn" value="Warn" />
                      <el-option label="Error" value="Error" />
                      <el-option label="Fatal" value="Fatal" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 连接 -->
            <div class="form-section">
              <h3 class="section-title">连接</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="通道" prop="channelId">
                    <div class="channel-select">
                      <el-select
                        v-model="formData.channelId"
                        placeholder="请选择..."
                        style="width: 100%"
                        filterable
                        clearable
                        @change="onChannelChanged"
                      >
                        <el-option
                          v-for="channel in channelList"
                          :key="channel.id"
                          :label="channel.name"
                          :value="channel.id"
                        >
                          <span>{{ channel.name }}</span>
                          <span class="channel-desc">{{ channel.description || '' }}</span>
                        </el-option>
                      </el-select>
                      <el-button
                        type="primary"
                        size="small"
                        class="add-channel-btn"
                        @click="handleAddChannel"
                      >
                        <el-icon><Plus /></el-icon>
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="默认执行间隔" prop="intervalTime">
                    <el-input
                      v-model="formData.intervalTime"
                      placeholder="请输入..."
                      clearable
                    >
                      <template #append>ms</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 冗余 -->
            <div class="form-section">
              <h3 class="section-title">冗余</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="启用冗余" prop="redundantEnable">
                    <el-switch
                      v-model="formData.redundantEnable"
                      active-text="启用"
                      inactive-text="禁用"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="冗余操作模式" prop="redundantSwitchType">
                    <el-select
                      v-model="formData.redundantSwitchType"
                      placeholder="请选择..."
                      style="width: 100%"
                      :disabled="!formData.redundantEnable"
                    >
                      <el-option label="OffLine" :value="0" />
                      <el-option label="OnLine" :value="1" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="冗余设备" prop="redundantDeviceId">
                    <el-select
                      v-model="formData.redundantDeviceId"
                      placeholder="请选择..."
                      style="width: 100%"
                      :disabled="!formData.redundantEnable"
                      clearable
                    >
                      <el-option
                        v-for="device in deviceList"
                        :key="device.id"
                        :label="device.name"
                        :value="device.id"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="冗余检测时间" prop="redundantScanIntervalTime">
                    <el-input
                      v-model="formData.redundantScanIntervalTime"
                      placeholder="请输入..."
                      :disabled="!formData.redundantEnable"
                      clearable
                    >
                      <template #append>ms</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="冗余检测脚本" prop="redundantScript">
                    <el-input
                      v-model="formData.redundantScript"
                      type="textarea"
                      :rows="3"
                      placeholder="请输入..."
                      :disabled="!formData.redundantEnable"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 备用 -->
            <div class="form-section">
              <h3 class="section-title">备用</h3>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="备用1" prop="remark1">
                    <el-input
                      v-model="formData.remark1"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备用2" prop="remark2">
                    <el-input
                      v-model="formData.remark2"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="备用3" prop="remark3">
                    <el-input
                      v-model="formData.remark3"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="备用4" prop="remark4">
                    <el-input
                      v-model="formData.remark4"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="备用5" prop="remark5">
                    <el-input
                      v-model="formData.remark5"
                      placeholder="请输入..."
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-tab-pane>
        
        <!-- 插件信息标签页 - 动态显示 -->
        <el-tab-pane 
          label="插件信息" 
          name="plugin" 
          v-if="showPluginTab"
        >
          <div class="plugin-form" v-loading="pluginLoading">
            <!-- 调试信息 -->
            <div class="debug-info" style="background: #f5f7fa; padding: 10px; margin-bottom: 20px; border-radius: 4px;">
              <p><strong>调试信息:</strong></p>
              <p>showPluginTab: {{ showPluginTab }}</p>
              <p>pluginLoading: {{ pluginLoading }}</p>
              <p>当前通道类型: {{ currentChannelType }}</p>
              <p>当前通道插件: {{ formData.channelId ? channelList.find(c => c.id === formData.channelId)?.pluginName : '未选择' }}</p>
              <p>当前通道pluginType: {{ formData.channelId ? channelList.find(c => c.id === formData.channelId)?.pluginType : '未选择' }}</p>
              <p>isCollectChannel: {{ isCollectChannel }}</p>
              <p>pluginProperties数量: {{ Object.keys(pluginProperties).length }}</p>
              <p>pluginPropertyEditorItems数量: {{ pluginPropertyEditorItems.length }}</p>
              <p>pluginProperties内容: {{ JSON.stringify(pluginProperties, null, 2) }}</p>
              <p>pluginPropertyEditorItems内容: {{ JSON.stringify(pluginPropertyEditorItems, null, 2) }}</p>
              
              <!-- 手动测试按钮 -->
              <div style="margin-top: 10px;">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="testPluginProperties"
                  :disabled="!formData.channelId"
                >
                  手动测试插件属性加载
                </el-button>
                <el-button 
                  type="warning" 
                  size="small" 
                  @click="clearPluginData"
                  style="margin-left: 10px;"
                >
                  清空插件数据
                </el-button>
              </div>
            </div>
            
            <!-- 采集通道配置界面 -->
            <div v-if="isCollectChannel" class="collect-channel-config">
              <h3 class="section-title">采集通道配置</h3>
              <el-form
                ref="collectFormRef"
                :model="collectChannelForm"
                :rules="collectChannelRules"
                label-width="140px"
                class="collect-channel-form"
              >
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="协议类型" prop="protocolType">
                      <el-select
                        v-model="collectChannelForm.protocolType"
                        placeholder="请选择..."
                        style="width: 100%"
                      >
                        <el-option label="ModbusTcp" value="ModbusTcp" />
                        <el-option label="ModbusRtu" value="ModbusRtu" />
                        <el-option label="OpcUa" value="OpcUa" />
                        <el-option label="S7" value="S7" />
                        <el-option label="Ethernet" value="Ethernet" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="默认站号" prop="defaultStation">
                      <el-input-number
                        v-model="collectChannelForm.defaultStation"
                        :min="1"
                        :max="255"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="Dtu注册包" prop="dtuRegisterPacket">
                      <el-input
                        v-model="collectChannelForm.dtuRegisterPacket"
                        placeholder="请输入..."
                        clearable
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="最大打包长度" prop="maxPacketLength">
                      <el-input-number
                        v-model="collectChannelForm.maxPacketLength"
                        :min="1"
                        :max="1000"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="读写超时时间" prop="timeout">
                      <el-input-number
                        v-model="collectChannelForm.timeout"
                        :min="100"
                        :max="30000"
                        style="width: 100%"
                      >
                        <template #append>ms</template>
                      </el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="发送延时" prop="sendDelay">
                      <el-input-number
                        v-model="collectChannelForm.sendDelay"
                        :min="0"
                        :max="1000"
                        style="width: 100%"
                      >
                        <template #append>ms</template>
                      </el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="字符串反转" prop="stringReverse">
                      <el-switch
                        v-model="collectChannelForm.stringReverse"
                        active-text="是"
                        inactive-text="否"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="解析规则" prop="parseRule">
                      <el-select
                        v-model="collectChannelForm.parseRule"
                        placeholder="请选择..."
                        style="width: 100%"
                      >
                        <el-option label="ABCD" value="ABCD" />
                        <el-option label="DCBA" value="DCBA" />
                        <el-option label="BADC" value="BADC" />
                        <el-option label="CDAB" value="CDAB" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="失败重试次数" prop="retryCount">
                      <el-input-number
                        v-model="collectChannelForm.retryCount"
                        :min="0"
                        :max="10"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            
            <!-- 业务通道配置界面 -->
            <div v-else-if="!isCollectChannel && (pluginProperties && Object.keys(pluginProperties).length > 0 || pluginPropertyEditorItems && pluginPropertyEditorItems.length > 0)">
              <h3 class="section-title">插件属性配置</h3>
              
              <!-- 如果有编辑器项，显示动态表单 -->
              <div v-if="pluginPropertyEditorItems && pluginPropertyEditorItems.length > 0">
                <el-form
                  ref="pluginFormRef"
                  :model="pluginProperties"
                  :rules="pluginFormRules"
                  label-width="140px"
                  class="plugin-properties-form"
                >
                  <el-row :gutter="20">
                    <el-col 
                      :span="12" 
                      v-for="item in pluginPropertyEditorItems" 
                      :key="item.field"
                    >
                      <el-form-item 
                        :label="formatFieldLabel(item.field)"
                        :required="item.required"
                        :prop="item.field"
                      >
                        <!-- 字符串类型 -->
                        <el-input 
                          v-if="resolveEditorType(item) === 'string'"
                          v-model="pluginProperties[item.field]"
                          :placeholder="item.defaultValue || `请输入${formatFieldLabel(item.field)}`"
                          clearable
                        />
                        
                        <!-- 整数类型 -->
                        <el-input-number 
                          v-else-if="resolveEditorType(item) === 'int'"
                          v-model="pluginProperties[item.field]"
                          :placeholder="item.defaultValue"
                          :min="0"
                          style="width: 100%"
                        />
                        
                        <!-- 浮点数类型 -->
                        <el-input-number 
                          v-else-if="resolveEditorType(item) === 'float' || resolveEditorType(item) === 'double'"
                          v-model="pluginProperties[item.field]"
                          :placeholder="item.defaultValue"
                          :precision="2"
                          style="width: 100%"
                        />
                        
                        <!-- 布尔类型 -->
                        <el-switch
                          v-else-if="resolveEditorType(item) === 'bool' || resolveEditorType(item) === 'boolean'"
                          v-model="pluginProperties[item.field]"
                          active-text="是"
                          inactive-text="否"
                        />
                        
                        <!-- 选择类型 -->
                        <el-select
                          v-else-if="resolveEditorType(item) === 'select' || resolveEditorType(item) === 'enum'"
                          v-model="pluginProperties[item.field]"
                          :placeholder="item.defaultValue"
                          style="width: 100%"
                          clearable
                        >
                          <el-option
                            v-for="option in getSelectOptions(item)"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          />
                        </el-select>
                        
                        <!-- 文本域类型 -->
                        <el-input
                          v-else-if="item.type === 'textarea'"
                          v-model="pluginProperties[item.field]"
                          type="textarea"
                          :rows="3"
                          :placeholder="item.defaultValue"
                          clearable
                        />
                        
                        <!-- 文件选择类型 -->
                        <div v-else-if="item.type === 'file' || item.field.toLowerCase().includes('file') || item.field.toLowerCase().includes('ca') || item.field.toLowerCase().includes('cert') || item.field.toLowerCase().includes('key')" class="file-input-group">
                          <el-input
                            v-model="pluginProperties[item.field]"
                            :placeholder="item.defaultValue || '请选择文件'"
                            readonly
                            style="flex: 1"
                          />
                          <el-button type="primary" size="small" @click="handleFileSelect(item.field)">
                            浏览
                          </el-button>
                        </div>
                        
                        <!-- 默认字符串类型 -->
                        <el-input 
                          v-else
                          v-model="pluginProperties[item.field]"
                          :placeholder="item.defaultValue || `请输入${formatFieldLabel(item.field)}`"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              
              <!-- 如果没有编辑器项，但有属性数据，显示属性列表 -->
              <div v-else-if="pluginProperties && Object.keys(pluginProperties).length > 0" class="plugin-properties-list">
                <h4>插件属性列表</h4>
                <el-form
                  ref="pluginFormRef"
                  :model="pluginProperties"
                  label-width="140px"
                  class="plugin-properties-form"
                >
                  <el-row :gutter="20">
                    <el-col 
                      :span="12" 
                      v-for="(value, key) in pluginProperties" 
                      :key="key"
                    >
                      <el-form-item :label="formatFieldLabel(key)">
                        <el-input 
                          v-model="pluginProperties[key]" 
                          :placeholder="`请输入${formatFieldLabel(key)}`"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              
              <!-- 属性配置为空时的提示 -->
              <div v-else class="plugin-empty">
                <el-empty description="暂无插件属性配置">
                  <p>请检查：</p>
                  <p>1. 是否选择了通道</p>
                  <p>2. 通道是否有对应的插件</p>
                  <p>3. 插件是否有属性配置</p>
                </el-empty>
              </div>
            </div>
            
            <div v-else-if="!pluginLoading" class="plugin-empty">
              <el-empty description="暂无插件属性配置">
                <p>请检查：</p>
                <p>1. 是否选择了通道</p>
                <p>2. 通道是否有对应的插件</p>
                <p>3. 插件是否有属性配置</p>
              </el-empty>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            <el-icon><Check /></el-icon>
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close, Check } from '@element-plus/icons-vue'
import { 
  getDeviceList, 
  batchSaveDevice, 
  getChannelList, 
  getPluginProperties,
  type DevicePayloadItem,
  type PluginPropertyEditorItem,
  type PluginPropertiesResponse
} from '@/api/gateway/device'

type DeviceRow = {
  id?: number
  name: string
  description?: string
  channelName?: string
  channelId?: number | string
  enable?: boolean
  activeTime?: string | number | Date | null
  deviceStatus?: number | string
  variableCount?: number
  pluginName?: string
  [key: string]: any
}

type ChannelItem = {
  id: number
  name: string
  description?: string
  pluginName?: string
  pluginType?: number
  [key: string]: any
}

const loading = ref(false)
const saveLoading = ref(false)
const pluginLoading = ref(false)
const tableData = ref<DeviceRow[]>([])
const channelList = ref<ChannelItem[]>([])
const deviceList = ref<DeviceRow[]>([])

// 弹窗相关
const dialogVisible = ref(false)
const activeTab = ref('device')
const formRef = ref()
const pluginFormRef = ref()
const collectFormRef = ref() // 新增：采集通道表单引用

// 插件相关
const showPluginTab = ref(false)
const pluginProperties = ref<Record<string, any>>({})
const pluginPropertyEditorItems = ref<PluginPropertyEditorItem[]>([])
const pluginFormRules = reactive<Record<string, any>>({})

// 表单数据
const formData = reactive<DevicePayloadItem>({
  id: 0,
  name: '',
  description: '',
  channelId: 0,
  intervalTime: '1000',
  enable: true,
  logLevel: 'Info',
  devicePropertys: {},
  redundantEnable: false,
  redundantDeviceId: null,
  redundantSwitchType: 0,
  redundantScanIntervalTime: 30000,
  redundantScript: '',
  remark1: '',
  remark2: '',
  remark3: '',
  remark4: '',
  remark5: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' }
  ],
  channelId: [
    { required: true, message: '请选择通道', trigger: 'change' }
  ],
  intervalTime: [
    { required: true, message: '请输入执行间隔', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  redundantScanIntervalTime: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ]
}

// 新增：采集通道表单数据
const collectChannelForm = reactive({
  protocolType: 'ModbusTcp',
  defaultStation: 1,
  dtuRegisterPacket: '',
  maxPacketLength: 100,
  timeout: 3000,
  sendDelay: 0,
  stringReverse: false,
  parseRule: 'ABCD',
  retryCount: 3
})

// 新增：采集通道表单验证规则
const collectChannelRules = {
  protocolType: [
    { required: true, message: '请选择协议类型', trigger: 'change' }
  ],
  defaultStation: [
    { required: true, message: '请输入默认站号', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  maxPacketLength: [
    { required: true, message: '请输入最大打包长度', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  timeout: [
    { required: true, message: '请输入读写超时时间', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  sendDelay: [
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ],
  retryCount: [
    { required: true, message: '请输入失败重试次数', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ]
}

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

function normalizeRow(item: Record<string, any>): DeviceRow {
  return {
    id: item.id ?? item.deviceId,
    name: item.name ?? '-',
    description: item.description ?? item.remark1 ?? item.remark ?? '-',
    channelName: item.channelName ?? item.channel?.name ?? '',
    channelId: item.channelId ?? item.channel?.id,
    enable: item.enable ?? item.enabled ?? false,
    activeTime: item.activeTime ?? item.lastActiveTime ?? item.lastChangeTime ?? null,
    deviceStatus: item.deviceStatus ?? item.status ?? item.state ?? (item.enable ? 'Running' : 'Stopped'),
    variableCount: item.variableCount ?? item.variablesCount ?? item.variableTotal ?? 0,
    pluginName: item.pluginName ?? item.fullPluginName ?? item.plugin?.name ?? ''
  }
}

function parsePaged(result: any) {
  // 支持多种分页返回格式
  if (!result) return { records: [], total: 0, current: pagination.currentPage, size: pagination.pageSize }
  const data = result.data?.data || result.data || result
  const records = data.records || data.list || []
  return {
    records,
    total: data.total ?? data.Total ?? records.length,
    current: data.current ?? data.pageIndex ?? pagination.currentPage,
    size: data.size ?? data.pageSize ?? pagination.pageSize
  }
}

async function loadData() {
  loading.value = true
  try {
    const res = await getDeviceList({ pageIndex: pagination.currentPage, pageSize: pagination.pageSize })
    const paged = parsePaged(res)
    tableData.value = (paged.records as any[]).map(normalizeRow)
    pagination.total = paged.total
    pagination.currentPage = paged.current
    pagination.pageSize = paged.size
  } catch (err) {
    console.error('加载设备列表失败:', err)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载通道列表
async function loadChannelList() {
  try {
    const res = await getChannelList()
    const paged = parsePaged(res)
    channelList.value = paged.records || []
  } catch (err) {
    console.error('加载通道列表失败:', err)
    ElMessage.error('加载通道列表失败')
  }
}

// 加载设备列表（用于冗余设备选择）
async function loadDeviceList() {
  try {
    const res = await getDeviceList({ pageIndex: 1, pageSize: 1000 })
    const paged = parsePaged(res)
    deviceList.value = (paged.records as any[]).map(normalizeRow)
  } catch (err) {
    console.error('加载设备列表失败:', err)
  }
}

// 通道选择变化时触发
async function onChannelChanged(channelId: number) {
  if (!channelId) {
    showPluginTab.value = false
    pluginProperties.value = {}
    pluginPropertyEditorItems.value = []
    isCollectChannel.value = false
    return
  }
  
  try {
    // 1. 获取选中通道的插件名称
    const selectedChannel = channelList.value.find(c => c.id === channelId)
    if (!selectedChannel || !selectedChannel.pluginName) {
      showPluginTab.value = false
      isCollectChannel.value = false
      return
    }
    
    // 2. 判断是否为采集通道
    // 根据插件名称判断是否为采集通道，而不是根据pluginType
    // 常见的采集插件包括：Modbus、OpcUa、S7、Ethernet等
    const collectPluginKeywords = ['Modbus', 'OpcUa', 'S7', 'Ethernet', 'Collect', 'Driver']
    const isCollect = collectPluginKeywords.some(keyword => 
      selectedChannel.pluginName?.includes(keyword)
    )
    isCollectChannel.value = isCollect
    
    console.log('插件名称:', selectedChannel.pluginName)
    console.log('pluginType:', selectedChannel.pluginType)
    console.log('判断为采集通道:', isCollect)
    
    // 3. 如果是采集通道，直接显示插件信息标签页
    if (isCollect) {
      showPluginTab.value = true
      return
    }
    
    // 4. 如果是业务通道，获取插件属性配置
    pluginLoading.value = true
    const response = await getPluginProperties(selectedChannel.pluginName)
    const data = response.data || response
    
    console.log('插件属性响应:', response)
    console.log('解析后的数据:', data)
    console.log('model:', data.model)
    console.log('editorItems:', data.editorItems)
    
    // 检查返回数据的有效性
    if (!data || (!data.model && !data.editorItems)) {
      console.warn('插件属性数据为空或格式不正确:', data)
      // 尝试其他可能的数据结构
      if (data && typeof data === 'object') {
        // 如果直接返回的是属性对象
        if (Object.keys(data).length > 0 && !data.model && !data.editorItems) {
          pluginProperties.value = { ...data }
          pluginPropertyEditorItems.value = []
          console.log('使用直接返回的属性数据:', pluginProperties.value)
        } else {
          // 尝试从其他字段获取
          const possibleModel = data.properties || data.props || data.config || data
          if (possibleModel && typeof possibleModel === 'object' && Object.keys(possibleModel).length > 0) {
            pluginProperties.value = { ...possibleModel }
            pluginPropertyEditorItems.value = []
            console.log('从其他字段获取的属性数据:', pluginProperties.value)
          } else {
            pluginProperties.value = {}
            pluginPropertyEditorItems.value = []
            console.warn('无法获取有效的插件属性数据')
          }
        }
      } else {
        pluginProperties.value = {}
        pluginPropertyEditorItems.value = []
      }
    } else {
      // 正常的数据结构
      pluginProperties.value = { ...(data.model || {}) }
      pluginPropertyEditorItems.value = data.editorItems || []
    }

    // 新增：如果没有editorItems，根据pluginProperties自动生成
    if (pluginPropertyEditorItems.value.length === 0 && Object.keys(pluginProperties.value).length > 0) {
      console.log('自动生成editorItems...')
      pluginPropertyEditorItems.value = generateEditorItemsFromProperties(pluginProperties.value)
      console.log('自动生成的editorItems:', pluginPropertyEditorItems.value)
    }

    // 构建校验规则（必填）
    buildPluginFormRules()
    
    // 5. 显示插件信息标签页
    showPluginTab.value = true
    console.log('showPluginTab设置为:', showPluginTab.value)
    console.log('pluginProperties:', pluginProperties.value)
    console.log('pluginPropertyEditorItems:', pluginPropertyEditorItems.value)
    
    // 6. 如果有已保存的设备属性，加载到表单中
    const deviceProps = formData.devicePropertys || {}
    if (Object.keys(deviceProps).length > 0) {
      loadSavedProperties()
    }
    
  } catch (error) {
    console.error('加载插件属性失败:', error)
    ElMessage.error('加载插件属性失败')
    showPluginTab.value = false
    isCollectChannel.value = false
  } finally {
    pluginLoading.value = false
  }
}

// 加载已保存的属性
function loadSavedProperties() {
  if (isCollectChannel.value) {
    // 采集通道：加载采集通道配置
    const savedProps = formData.devicePropertys ?? {}
    Object.keys(collectChannelForm).forEach(key => {
      if (Object.prototype.hasOwnProperty.call(savedProps, key)) {
        (collectChannelForm as any)[key] = savedProps[key]
      }
    })
  } else if (pluginProperties.value) {
    // 业务通道：加载插件属性配置
    const savedProps = formData.devicePropertys ?? {}
    Object.keys(savedProps as Record<string, any>).forEach(key => {
      if (Object.prototype.hasOwnProperty.call(pluginProperties.value, key)) {
        pluginProperties.value[key] = (savedProps as any)[key]
      }
    })
  }
}

// 收集插件属性
function collectPluginProperties(): Record<string, any> {
  const properties: Record<string, any> = {}
  
  if (isCollectChannel.value) {
    // 采集通道：收集采集通道配置
    Object.keys(collectChannelForm).forEach(key => {
      const value = (collectChannelForm as any)[key]
      if (value !== null && value !== undefined && value !== '') {
        properties[key] = value
      }
    })
  } else if (pluginProperties.value) {
    // 业务通道：收集插件属性配置
    Object.keys(pluginProperties.value).forEach(key => {
      const value = pluginProperties.value[key]
      if (value !== null && value !== undefined && value !== '') {
        properties[key] = value
      }
    })
  }
  
  console.log('收集到的插件属性:', properties)
  return properties
}

// 获取选择类型的选项（预留功能）
function getSelectOptions(item: PluginPropertyEditorItem) {
  const fieldLower = item.field.toLowerCase()
  
  // 为常见字段提供预设选项
  if (fieldLower.includes('qos')) {
    return [
      { label: 'AtMostOnce', value: 'AtMostOnce' },
      { label: 'AtLeastOnce', value: 'AtLeastOnce' },
      { label: 'ExactlyOnce', value: 'ExactlyOnce' }
    ]
  } else if (fieldLower.includes('version') || fieldLower.includes('protocol')) {
    return [
      { label: 'V311', value: 'V311' },
      { label: 'V5', value: 'V5' },
      { label: 'V3.1.1', value: 'V3.1.1' }
    ]
  } else if (fieldLower.includes('mode') || fieldLower.includes('upload')) {
    return [
      { label: '变化', value: 'Change' },
      { label: '定时', value: 'Timed' },
      { label: '手动', value: 'Manual' }
    ]
  } else if (fieldLower.includes('websocket')) {
    return [
      { label: '启用', value: 'true' },
      { label: '禁用', value: 'false' }
    ]
  }
  
  // 如果defaultValue包含逗号分隔的选项，解析它们
  if (item.defaultValue && item.defaultValue.includes(',')) {
    return item.defaultValue.split(',').map(opt => ({
      label: opt.trim(),
      value: opt.trim()
    }))
  }
  
  return []
}

// 统一解析后端不同字段命名的类型
function resolveEditorType(item: PluginPropertyEditorItem): string {
  const t = (item.type || '').toString().toLowerCase()
  if (t.includes('enum')) return 'enum'
  if (t === 'string') return 'string'
  if (t === 'boolean' || t === 'bool') return 'boolean'
  if (t === 'int' || t === 'int32' || t === 'int64') return 'int'
  if (t === 'float' || t === 'double' || t === 'decimal') return 'double'
  if (t === 'textarea' || t === 'text') return 'textarea'
  if (t === 'select') return 'select'
  if (t === 'file') return 'file' // 新增：文件类型
  return t || 'string'
}

// 根据 editorItems 构建必填规则
function buildPluginFormRules() {
  // 清空旧规则
  Object.keys(pluginFormRules).forEach(k => delete pluginFormRules[k])
  pluginPropertyEditorItems.value.forEach(item => {
    if ((item as any).required === true || (item as any).isRequired === true) {
      pluginFormRules[item.field] = [
        { required: true, message: `${item.label || (item as any).text || item.field}不能为空`, trigger: 'blur' }
      ]
    }
  })
}

function handleSizeChange(size: number) {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadData()
}

function handleCurrentChange(page: number) {
  pagination.currentPage = page
  loadData()
}

function formatDateTime(value: any): string {
  if (!value) return '-'
  try {
    let date: Date
    if (value instanceof Date) date = value
    else if (typeof value === 'number') date = new Date(value > 1e12 ? value : value * 1000)
    else date = new Date(value)
    if (Number.isNaN(date.getTime())) return '-'
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  } catch {
    return '-'
  }
}

function getStatusText(row: DeviceRow): string {
  const status = row.deviceStatus
  if (typeof status === 'string') {
    const s = status.toLowerCase()
    if (s.includes('run')) return '运行中'
    if (s.includes('pause')) return '已暂停'
    if (s.includes('error') || s.includes('fault')) return '异常'
    if (s.includes('stop')) return '已停止'
    return status
  }
  if (typeof status === 'number') {
    // 0:停止 1:运行 2:暂停 3:异常
    return ({ 0: '已停止', 1: '运行中', 2: '已暂停', 3: '异常' } as any)[status] ?? (row.enable ? '运行中' : '已停止')
  }
  return row.enable ? '运行中' : '已停止'
}

function getStatusTag(row: DeviceRow): 'success' | 'warning' | 'danger' | 'info' {
  const text = getStatusText(row)
  if (text === '运行中') return 'success'
  if (text === '已暂停') return 'warning'
  if (text === '异常') return 'danger'
  if (text === '已停止') return 'info'
  return 'info'
}

// 显示新增弹窗
function showAddDialog() {
  resetForm()
  dialogVisible.value = true
  loadChannelList()
  loadDeviceList()
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    id: 0,
    name: '',
    description: '',
    channelId: 0,
    intervalTime: '1000',
    enable: true,
    logLevel: 'Info',
    devicePropertys: {},
    redundantEnable: false,
    redundantDeviceId: null,
    redundantSwitchType: 0,
    redundantScanIntervalTime: 30000,
    redundantScript: '',
    remark1: '',
    remark2: '',
    remark3: '',
    remark4: '',
    remark5: ''
  })
  
  // 重置插件相关
  showPluginTab.value = false
  pluginProperties.value = {}
  pluginPropertyEditorItems.value = []
  isCollectChannel.value = false // 重置采集通道状态
  collectChannelForm.protocolType = 'ModbusTcp' // 重置采集通道表单
  collectChannelForm.defaultStation = 1
  collectChannelForm.dtuRegisterPacket = ''
  collectChannelForm.maxPacketLength = 100
  collectChannelForm.timeout = 3000
  collectChannelForm.sendDelay = 0
  collectChannelForm.stringReverse = false
  collectChannelForm.parseRule = 'ABCD'
  collectChannelForm.retryCount = 3
  
  formRef.value?.clearValidate()
  pluginFormRef.value?.clearValidate()
  collectFormRef.value?.clearValidate() // 重置采集通道表单验证
}

// 保存设备
async function handleSave() {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 如果是采集通道，验证采集通道表单
    if (isCollectChannel.value && collectFormRef.value) {
      await collectFormRef.value.validate()
    }
    
    saveLoading.value = true
    
    // 1. 收集插件属性配置
    if (isCollectChannel.value) {
      // 采集通道：收集采集通道配置
      formData.devicePropertys = { ...collectChannelForm }
      console.log('保存采集通道配置:', formData.devicePropertys)
    } else if (showPluginTab.value && (pluginProperties.value || pluginPropertyEditorItems.value)) {
      // 业务通道：收集插件属性配置
      formData.devicePropertys = collectPluginProperties()
      console.log('保存业务通道配置:', formData.devicePropertys)
    }
    
    const deviceData: DevicePayloadItem = {
      ...formData,
      channelId: Number(formData.channelId),
      intervalTime: String(formData.intervalTime),
      redundantScanIntervalTime: Number(formData.redundantScanIntervalTime),
      redundantDeviceId: formData.redundantDeviceId == null
        ? null
        : Number(formData.redundantDeviceId)
    }
    
    await batchSaveDevice([deviceData], 0, true)
    ElMessage.success('设备添加成功')
    closeDialog()
    loadData() // 重新加载列表
  } catch (err) {
    console.error('保存设备失败:', err)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 添加通道（预留功能）
function handleAddChannel() {
  ElMessage.info('通道管理功能待实现')
}

// 新增：判断当前通道是否为采集通道
const isCollectChannel = ref(false)

// 新增：当前通道类型显示
const currentChannelType = computed(() => {
  if (!formData.channelId) return '未选择'
  const selectedChannel = channelList.value.find(c => c.id === formData.channelId)
  if (!selectedChannel) return '未知'
  
  // 使用相同的判断逻辑
  const collectPluginKeywords = ['Modbus', 'OpcUa', 'S7', 'Ethernet', 'Collect', 'Driver']
  const isCollect = collectPluginKeywords.some(keyword => 
    selectedChannel.pluginName?.includes(keyword)
  )
  
  if (isCollect) {
    return '采集通道'
  } else {
    return '业务通道'
  }
})

// 新增：手动测试插件属性加载
function testPluginProperties() {
  if (!formData.channelId) {
    ElMessage.warning('请先选择通道')
    return
  }
  onChannelChanged(Number(formData.channelId))
}

// 新增：清空插件数据
function clearPluginData() {
  ElMessageBox.confirm('确定要清空当前插件的属性数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    pluginProperties.value = {}
    pluginPropertyEditorItems.value = []
    ElMessage.success('插件属性数据已清空')
  }).catch(() => {
    // 用户取消
  })
}

// 新增：根据pluginProperties自动生成editorItems
function generateEditorItemsFromProperties(properties: Record<string, any>): PluginPropertyEditorItem[] {
  const items: PluginPropertyEditorItem[] = []
  Object.keys(properties).forEach(key => {
    const value = properties[key]
    let type = 'string' // 默认类型
    let defaultValue = ''

    // 根据字段名和值的类型来判断字段类型
    const fieldLower = key.toLowerCase()
    
    // 特殊字段类型判断
    if (fieldLower.includes('port') || fieldLower.includes('timeout') || fieldLower.includes('interval') || fieldLower.includes('count') || fieldLower.includes('size') || fieldLower.includes('length')) {
      type = 'int'
      defaultValue = String(value || 0)
    } else if (fieldLower.includes('enable') || fieldLower.includes('allow') || fieldLower.includes('use') || fieldLower.includes('upload') || fieldLower.includes('cache') || fieldLower.includes('filter') || fieldLower.includes('group') || fieldLower.includes('websocket') || fieldLower.includes('tls') || fieldLower.includes('rpc') || fieldLower.includes('json') || fieldLower.includes('detailed')) {
      type = 'bool'
      defaultValue = String(value || false)
    } else if (fieldLower.includes('version') || fieldLower.includes('qos') || fieldLower.includes('mode') || fieldLower.includes('protocol')) {
      type = 'select'
      defaultValue = String(value || '')
    } else if (fieldLower.includes('file') || fieldLower.includes('ca') || fieldLower.includes('cert') || fieldLower.includes('key')) {
      type = 'file'
      defaultValue = String(value || '')
    } else if (typeof value === 'boolean' || value === 'True' || value === 'False') {
      type = 'bool'
      defaultValue = String(value || false)
    } else if (typeof value === 'number' || !isNaN(Number(value))) {
      if (Number.isInteger(Number(value))) {
        type = 'int'
        defaultValue = String(value || 0)
      } else {
        type = 'float'
        defaultValue = String(value || 0)
      }
    } else if (typeof value === 'string') {
      if (value.includes(',')) {
        type = 'select'
        defaultValue = value
      } else if (value.length > 50) {
        type = 'textarea'
        defaultValue = ''
      } else {
        type = 'string'
        defaultValue = ''
      }
    }

    items.push({
      field: key,
      label: formatFieldLabel(key),
      type: type,
      required: false, // 默认非必填
      defaultValue: defaultValue
    })
  })
  return items
}

// 新增：格式化字段标签，首字母大写
function formatFieldLabel(field: string): string {
  if (!field) return ''
  return field.charAt(0).toUpperCase() + field.slice(1)
}

// 新增：处理文件选择
function handleFileSelect(field: string) {
  ElMessage.info(`文件选择功能待实现，字段: ${field}`)
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.device-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-header h2 {
  margin: 0;
  color: #303133;
}

.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 弹窗样式 */
.device-tabs {
  margin-top: -20px;
}

.device-form {
  padding: 20px 0;
}

.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409eff;
}

.channel-select {
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-channel-btn {
  flex-shrink: 0;
}

.channel-desc {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

/* 插件表单样式 */
.plugin-form {
  padding: 20px 0;
}

.plugin-properties-form {
  margin-top: 20px;
}

.plugin-empty {
  padding: 40px 0;
  text-align: center;
}

.debug-info {
  background-color: #f5f7fa;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 6px;
  font-size: 13px;
  color: #606266;
  border-left: 4px solid #409eff;
}

.debug-info p {
  margin: 5px 0;
  line-height: 1.4;
}

.debug-info strong {
  color: #303133;
}

/* 手动测试按钮样式 */
.debug-info .el-button {
  margin-top: 10px;
  margin-right: 10px;
}

.debug-info .el-button:last-child {
  margin-right: 0;
}

.plugin-properties-list .el-descriptions {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 4px;
}

:deep(.el-input .el-input-group__append) {
  background-color: #f5f7fa;
  border-left: 1px solid #dcdfe6;
}

:deep(.el-input-number .el-input__wrapper) {
  border-radius: 4px;
}

/* 新增：采集通道配置样式 */
.collect-channel-config {
  padding: 20px 0;
}

.collect-channel-form {
  margin-top: 20px;
}

.collect-channel-form .el-form-item {
  margin-bottom: 20px;
}

.collect-channel-form .el-form-item__label {
  font-weight: 500;
}

/* 新增：文件选择弹窗样式 */
.file-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-select-dialog .el-dialog__body {
  padding: 20px;
}

/* 新增：插件属性配置样式 */
.plugin-properties-form {
  margin-top: 20px;
}

.plugin-properties-form .el-form-item {
  margin-bottom: 20px;
}

.plugin-properties-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.plugin-properties-form .el-input,
.plugin-properties-form .el-input-number,
.plugin-properties-form .el-select {
  width: 100%;
}

.plugin-properties-form .el-switch {
  margin-top: 4px;
}

.plugin-properties-form .el-switch .el-switch__label {
  font-size: 12px;
}

/* 采集通道配置样式 */
.collect-channel-config {
  padding: 20px 0;
}

.collect-channel-form {
  margin-top: 20px;
}

.collect-channel-form .el-form-item {
  margin-bottom: 20px;
}

.collect-channel-form .el-form-item__label {
  font-weight: 500;
}
</style>