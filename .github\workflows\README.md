# GitHub Actions 工作流说明文档

本目录包含了项目的GitHub Actions自动化工作流配置。这些工作流会在特定条件下自动执行，用于保证代码质量、自动化测试和发布管理。

## 📋 工作流概览

| 工作流文件 | 触发条件 | 主要功能 | 执行时间 |
|-----------|---------|---------|---------|
| `lint.yml` | 推送到master分支<br/>创建/更新PR | 代码质量检查和格式化 | ~3-5分钟 |
| `changelog.yml` | 推送版本标签(v*) | 自动生成发布日志 | ~1-2分钟 |

## 🔧 详细说明

### 1. lint.yml - 代码质量检查工作流

#### 触发条件
```yaml
on:
  push:
    branches:
      - master        # 推送到master分支时
  pull_request:       # 创建或更新Pull Request时
```

#### 执行环境
- **操作系统**: Ubuntu Latest
- **Python版本**: 3.10, 3.11, 3.12, 3.13 (矩阵构建)
- **包管理器**: uv (现代Python包管理器)

#### 执行步骤
1. **代码检出**: 获取最新代码
2. **环境准备**: 安装uv包管理器
3. **Python设置**: 配置指定版本的Python环境
4. **依赖安装**: 安装lint相关依赖包
5. **代码检查**: 运行pre-commit钩子进行全面检查

#### 检查内容
- **Ruff代码检查**: Python代码风格、语法错误、潜在bug检测
- **代码格式化**: 自动修复代码格式问题
- **YAML文件检查**: 验证YAML配置文件格式
- **TOML文件检查**: 验证TOML配置文件格式
- **依赖管理**: 更新uv.lock和requirements.txt文件

#### 配置文件
- **Pre-commit配置**: `.pre-commit-config.yaml`
- **Ruff配置**: `backend/.ruff.toml`
- **执行脚本**: `backend/scripts/lint.sh`

### 2. changelog.yml - 发布日志生成工作流

#### 触发条件
```yaml
on:
  push:
    tags:
      - v*            # 推送版本标签时 (如: v1.0.0, v2.1.3)
```

#### 执行环境
- **操作系统**: Ubuntu Latest
- **目标分支**: master

#### 执行步骤
1. **代码检出**: 检出master分支代码
2. **日志生成**: 基于GitHub Releases自动生成CHANGELOG.md
3. **PR创建**: 自动创建Pull Request更新日志文件

#### 功能特性
- 自动从GitHub Releases提取发布信息
- 生成标准格式的CHANGELOG.md文件
- 支持自动化版本发布流程

## 🚀 使用指南

### 开发者日常工作流

#### 1. 功能开发
```bash
# 创建功能分支
git checkout -b feature/your-feature

# 开发和提交代码
git add .
git commit -m "feat: 添加新功能"

# 推送分支
git push origin feature/your-feature
```

#### 2. 代码检查
```bash
# 本地运行代码检查 (推荐在提交前执行)
pip install pre-commit
pre-commit run --all-files

# 或者使用项目脚本
chmod +x backend/scripts/lint.sh
./backend/scripts/lint.sh
```

#### 3. 创建Pull Request
- 从功能分支向master分支创建PR
- **自动触发**: lint.yml工作流会自动执行代码检查
- **检查结果**: 在PR页面查看Actions执行状态
- **合并条件**: 所有检查通过后才能合并

#### 4. 版本发布
```bash
# 创建版本标签
git tag v1.0.0
git push origin v1.0.0

# 自动触发: changelog.yml工作流会自动生成发布日志
```

### 管理员操作

#### 查看Actions执行状态
1. 进入GitHub仓库页面
2. 点击"Actions"标签
3. 查看具体工作流的执行日志
4. 分析失败原因并指导开发者修复

#### 配置管理
- **Secrets管理**: 在仓库设置中配置`GH_TOKEN`等敏感信息
- **分支保护**: 设置master分支保护规则，要求Actions检查通过
- **权限控制**: 管理工作流的执行权限

## ⚠️ 常见问题和解决方案

### 1. lint检查失败

**问题**: Ruff检查发现代码风格问题
```
ruff check failed with exit code 1
```

**解决方案**:
```bash
# 本地修复代码风格问题
ruff check --fix backend/
ruff format backend/

# 重新提交
git add .
git commit -m "fix: 修复代码风格问题"
```

### 2. 依赖管理问题

**问题**: uv.lock文件过期
```
uv.lock is out of date
```

**解决方案**:
```bash
# 更新依赖锁文件
uv lock

# 提交更新
git add uv.lock
git commit -m "chore: 更新依赖锁文件"
```

### 3. YAML/TOML格式错误

**问题**: 配置文件格式不正确
```
check-yaml failed
```

**解决方案**:
- 检查YAML文件的缩进和语法
- 使用在线YAML/TOML验证器检查格式
- 修复后重新提交

### 4. Python版本兼容性问题

**问题**: 代码在某个Python版本下失败
```
Python 3.13 build failed
```

**解决方案**:
- 检查代码是否使用了特定版本的语法特性
- 更新代码以兼容所有支持的Python版本
- 或者调整工作流中的Python版本矩阵

## 📊 性能优化

### 缓存策略
- **依赖缓存**: uv自动缓存Python包，加速后续构建
- **Python缓存**: GitHub Actions自动缓存Python环境

### 并行执行
- **矩阵构建**: 多个Python版本并行执行，提高效率
- **失败快速**: `fail-fast: false` 确保所有版本都被测试

## 🔒 安全考虑

### Secrets管理
- `GH_TOKEN`: 用于changelog生成的GitHub访问令牌
- 敏感信息不应硬编码在工作流文件中

### 权限控制
- 工作流只能访问必要的仓库权限
- 第三方Actions使用固定版本标签

## 📈 监控和维护

### 定期维护任务
1. **更新Actions版本**: 定期更新使用的GitHub Actions版本
2. **依赖更新**: 更新pre-commit钩子和相关工具版本
3. **Python版本**: 根据项目需求调整支持的Python版本

### 监控指标
- **执行时间**: 监控工作流执行时间，优化性能
- **成功率**: 跟踪检查通过率，识别常见问题
- **资源使用**: 监控GitHub Actions使用量

## 📞 联系方式

如有问题或建议，请联系：
- **技术负责人**: [技术负责人邮箱]
- **DevOps团队**: [DevOps团队邮箱]
- **项目地址**: https://git.978543210.com/IOT-RD/fastapi_best_architecture.git

## 🔍 工作流文件详解

### lint.yml 完整配置解析

```yaml
name: ci                    # 工作流名称

on:                        # 触发条件
  push:
    branches:
      - master             # 只有推送到master分支才触发
  pull_request:            # 任何PR都会触发

jobs:
  lint:                    # 作业名称
    runs-on: ubuntu-latest # 运行环境
    name: lint ${{ matrix.python-version }}
    strategy:              # 矩阵策略
      matrix:
        python-version: [ '3.10', '3.11', '3.12', '3.13' ]
      fail-fast: false    # 不快速失败，确保所有版本都测试
```

### changelog.yml 完整配置解析

```yaml
name: Release changelog    # 工作流名称

on:                       # 触发条件
  push:
    tags:
      - v*               # 匹配 v1.0.0, v2.1.3 等版本标签

jobs:
  changelog:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: master      # 检出master分支

      - uses: rhysd/changelog-from-release/action@v3
        with:
          file: CHANGELOG.md
          pull_request: true
          github_token: ${{ secrets.GH_TOKEN }}
```

## 📚 相关配置文件说明

### .pre-commit-config.yaml
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-yaml     # 检查YAML文件格式
      - id: check-toml     # 检查TOML文件格式

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.11.9
    hooks:
      - id: ruff           # Python代码检查
        args:
          - '--config'
          - 'backend/.ruff.toml'
          - '--fix'
          - '--unsafe-fixes'
      - id: ruff-format    # Python代码格式化

  - repo: https://github.com/astral-sh/uv-pre-commit
    rev: 0.7.3
    hooks:
      - id: uv-lock        # 更新uv.lock文件
      - id: uv-export      # 导出requirements.txt
```

### backend/scripts/lint.sh
```bash
#!/usr/bin/env bash
pre-commit run --all-files
```

## 🎯 最佳实践

### 1. 提交前本地检查
```bash
# 安装pre-commit
pip install pre-commit

# 安装钩子
pre-commit install

# 手动运行检查
pre-commit run --all-files
```

### 2. 提交信息规范
遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```bash
# 功能添加
git commit -m "feat: 添加用户认证功能"

# 问题修复
git commit -m "fix: 修复登录页面样式问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 代码重构
git commit -m "refactor: 重构用户服务层代码"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试相关
git commit -m "test: 添加用户服务单元测试"

# 构建相关
git commit -m "build: 更新依赖包版本"

# CI/CD相关
git commit -m "ci: 更新GitHub Actions配置"

# 其他杂项
git commit -m "chore: 清理无用代码"
```

### 3. 分支管理策略
```bash
# 主分支
master                     # 生产环境代码

# 开发分支
develop                    # 开发环境代码

# 功能分支
feature/user-auth         # 用户认证功能
feature/api-optimization  # API优化

# 修复分支
hotfix/login-bug          # 紧急修复登录问题

# 发布分支
release/v1.2.0            # 版本发布准备
```

## 🚨 故障排除指南

### Actions执行失败的常见原因

#### 1. 代码风格问题
```
Error: Ruff found 15 errors
```
**解决步骤**:
1. 本地运行 `ruff check --fix backend/`
2. 运行 `ruff format backend/`
3. 提交修复后的代码

#### 2. 依赖冲突
```
Error: Could not resolve dependencies
```
**解决步骤**:
1. 检查 `pyproject.toml` 中的依赖版本
2. 运行 `uv lock --upgrade` 更新锁文件
3. 测试本地环境是否正常

#### 3. 测试失败
```
Error: 5 tests failed
```
**解决步骤**:
1. 本地运行测试 `pytest`
2. 修复失败的测试用例
3. 确保所有测试通过后再提交

#### 4. 权限问题
```
Error: Permission denied
```
**解决步骤**:
1. 检查 `GH_TOKEN` 是否正确配置
2. 确认token具有必要的权限
3. 联系管理员检查仓库权限设置

### 调试技巧

#### 1. 查看详细日志
- 点击失败的Actions
- 展开具体的步骤
- 查看完整的错误信息

#### 2. 本地复现问题
```bash
# 使用相同的Python版本
pyenv install 3.11
pyenv local 3.11

# 使用相同的依赖管理器
pip install uv
uv sync

# 运行相同的检查命令
./backend/scripts/lint.sh
```

#### 3. 逐步调试
```bash
# 单独运行各个检查
ruff check backend/
ruff format --check backend/
pre-commit run check-yaml --all-files
pre-commit run check-toml --all-files
```

---

*最后更新时间: 2025-08-12*
*文档版本: v1.0.0*
