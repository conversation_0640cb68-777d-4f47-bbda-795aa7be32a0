#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传处理工具

提供文件上传的高级功能，包括分块上传、进度跟踪、验证等
"""
import os
import hashlib
import mimetypes
from typing import AsyncGenerator, Dict, List, Optional, Tuple
from datetime import datetime
import aiofiles
from fastapi import HTTPException, UploadFile
from loguru import logger

from backend.app.iot.schema.document import FileValidation, FileUploadProgress


class FileUploadHandler:
    """文件上传处理器"""
    
    def __init__(self, temp_dir: str = "/tmp/uploads"):
        self.temp_dir = temp_dir
        self.validation = FileValidation()
        self.chunk_size = 8192  # 8KB chunks for reading
        self.progress_callbacks: Dict[str, callable] = {}
        
        # 确保临时目录存在
        os.makedirs(temp_dir, exist_ok=True)
    
    def register_progress_callback(self, upload_id: str, callback: callable):
        """注册进度回调函数"""
        self.progress_callbacks[upload_id] = callback
    
    def unregister_progress_callback(self, upload_id: str):
        """注销进度回调函数"""
        if upload_id in self.progress_callbacks:
            del self.progress_callbacks[upload_id]
    
    async def _notify_progress(self, upload_id: str, progress: FileUploadProgress):
        """通知上传进度"""
        if upload_id in self.progress_callbacks:
            try:
                await self.progress_callbacks[upload_id](progress)
            except Exception as e:
                logger.warning(f"进度回调失败: {e}")
    
    def _validate_file_basic(self, file: UploadFile) -> None:
        """基础文件验证"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件大小
        if file.size and file.size > self.validation.max_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 {self.validation.max_size / 1024 / 1024:.1f}MB"
            )
        
        # 检查文件扩展名
        file_ext = os.path.splitext(file.filename)[1].lower().lstrip('.')
        if file_ext not in self.validation.allowed_types:
            raise HTTPException(
                status_code=415,
                detail=f"不支持的文件类型: {file_ext}，支持的类型: {', '.join(self.validation.allowed_types)}"
            )
        
        # 检查MIME类型
        if file.content_type:
            if file.content_type not in self.validation.allowed_mime_types:
                # 尝试根据文件扩展名推断MIME类型
                guessed_type, _ = mimetypes.guess_type(file.filename)
                if not guessed_type or guessed_type not in self.validation.allowed_mime_types:
                    raise HTTPException(
                        status_code=415,
                        detail=f"不支持的MIME类型: {file.content_type}"
                    )
    
    def _calculate_file_hash(self, content: bytes) -> str:
        """计算文件哈希值"""
        return hashlib.md5(content).hexdigest()
    
    def _get_file_info(self, file: UploadFile, content: bytes) -> Dict:
        """获取文件信息"""
        file_hash = self._calculate_file_hash(content)
        file_ext = os.path.splitext(file.filename or "")[1].lower().lstrip('.')
        
        return {
            "filename": file.filename,
            "size": len(content),
            "content_type": file.content_type,
            "extension": file_ext,
            "hash": file_hash,
            "upload_time": datetime.now().isoformat()
        }
    
    async def _save_temp_file(self, content: bytes, filename: str) -> str:
        """保存临时文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_filename = f"{timestamp}_{filename}"
        temp_path = os.path.join(self.temp_dir, temp_filename)
        
        async with aiofiles.open(temp_path, 'wb') as f:
            await f.write(content)
        
        return temp_path
    
    async def upload_file_with_progress(
        self, 
        file: UploadFile, 
        upload_id: Optional[str] = None
    ) -> Tuple[bytes, Dict]:
        """
        上传文件并跟踪进度
        
        :param file: 上传的文件
        :param upload_id: 上传ID，用于进度跟踪
        :return: (文件内容, 文件信息)
        """
        # 基础验证
        self._validate_file_basic(file)
        
        # 生成上传ID
        if not upload_id:
            upload_id = hashlib.md5(f"{file.filename}_{datetime.now()}".encode()).hexdigest()
        
        # 初始化进度
        total_size = file.size or 0
        uploaded_size = 0
        start_time = datetime.now()
        
        progress = FileUploadProgress(
            file_name=file.filename or "unknown",
            total_size=total_size,
            uploaded_size=0,
            progress=0.0,
            status="uploading"
        )
        
        await self._notify_progress(upload_id, progress)
        
        # 读取文件内容
        content_chunks = []
        
        try:
            while True:
                chunk = await file.read(self.chunk_size)
                if not chunk:
                    break
                
                content_chunks.append(chunk)
                uploaded_size += len(chunk)
                
                # 更新进度
                if total_size > 0:
                    progress.uploaded_size = uploaded_size
                    progress.progress = min(uploaded_size / total_size, 1.0)
                    
                    # 计算速度和剩余时间
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    if elapsed_time > 0:
                        speed = uploaded_size / elapsed_time  # bytes/second
                        progress.speed = f"{speed / 1024:.1f} KB/s"
                        
                        if speed > 0 and uploaded_size < total_size:
                            remaining_bytes = total_size - uploaded_size
                            remaining_seconds = remaining_bytes / speed
                            progress.remaining_time = f"{remaining_seconds:.1f}s"
                
                await self._notify_progress(upload_id, progress)
        
        except Exception as e:
            progress.status = "error"
            await self._notify_progress(upload_id, progress)
            raise HTTPException(status_code=500, detail=f"文件读取失败: {str(e)}")
        
        # 合并内容
        content = b''.join(content_chunks)
        
        # 最终验证文件大小
        if len(content) > self.validation.max_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件大小超过限制 {self.validation.max_size / 1024 / 1024:.1f}MB"
            )
        
        # 获取文件信息
        file_info = self._get_file_info(file, content)
        
        # 完成进度
        progress.uploaded_size = len(content)
        progress.progress = 1.0
        progress.status = "completed"
        await self._notify_progress(upload_id, progress)
        
        logger.info(f"文件上传完成: {file.filename}, 大小: {len(content)} bytes")
        
        return content, file_info
    
    async def upload_large_file_chunked(
        self, 
        file: UploadFile, 
        chunk_size: int = 1024 * 1024,  # 1MB chunks
        upload_id: Optional[str] = None
    ) -> AsyncGenerator[Tuple[bytes, Dict, bool], None]:
        """
        分块上传大文件
        
        :param file: 上传的文件
        :param chunk_size: 分块大小
        :param upload_id: 上传ID
        :yield: (chunk_data, file_info, is_last_chunk)
        """
        # 基础验证
        self._validate_file_basic(file)
        
        if not upload_id:
            upload_id = hashlib.md5(f"{file.filename}_{datetime.now()}".encode()).hexdigest()
        
        total_size = file.size or 0
        uploaded_size = 0
        chunk_index = 0
        start_time = datetime.now()
        
        # 初始化进度
        progress = FileUploadProgress(
            file_name=file.filename or "unknown",
            total_size=total_size,
            uploaded_size=0,
            progress=0.0,
            status="uploading"
        )
        
        await self._notify_progress(upload_id, progress)
        
        try:
            while True:
                chunk = await file.read(chunk_size)
                if not chunk:
                    break
                
                uploaded_size += len(chunk)
                chunk_index += 1
                is_last_chunk = uploaded_size >= total_size or len(chunk) < chunk_size
                
                # 更新进度
                if total_size > 0:
                    progress.uploaded_size = uploaded_size
                    progress.progress = min(uploaded_size / total_size, 1.0)
                    
                    # 计算速度
                    elapsed_time = (datetime.now() - start_time).total_seconds()
                    if elapsed_time > 0:
                        speed = uploaded_size / elapsed_time
                        progress.speed = f"{speed / 1024:.1f} KB/s"
                
                await self._notify_progress(upload_id, progress)
                
                # 构建分块信息
                chunk_info = {
                    "chunk_index": chunk_index,
                    "chunk_size": len(chunk),
                    "uploaded_size": uploaded_size,
                    "total_size": total_size,
                    "is_last_chunk": is_last_chunk,
                    "filename": file.filename,
                    "content_type": file.content_type
                }
                
                yield chunk, chunk_info, is_last_chunk
                
                if is_last_chunk:
                    break
        
        except Exception as e:
            progress.status = "error"
            await self._notify_progress(upload_id, progress)
            raise HTTPException(status_code=500, detail=f"分块上传失败: {str(e)}")
        
        # 完成进度
        progress.status = "completed"
        progress.progress = 1.0
        await self._notify_progress(upload_id, progress)
        
        logger.info(f"分块上传完成: {file.filename}, 总大小: {uploaded_size} bytes, 分块数: {chunk_index}")
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """清理临时文件"""
        try:
            current_time = datetime.now()
            for filename in os.listdir(self.temp_dir):
                file_path = os.path.join(self.temp_dir, filename)
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    age_hours = (current_time - file_time).total_seconds() / 3600
                    
                    if age_hours > max_age_hours:
                        os.remove(file_path)
                        logger.info(f"清理临时文件: {filename}")
        
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")


# 创建全局实例
file_upload_handler = FileUploadHandler()
