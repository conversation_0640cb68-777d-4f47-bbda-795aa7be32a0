#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统权限控制中间件
集成Java系统的权限查询服务到FastAPI权限控制中
"""

from typing import Callable, Optional
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from backend.service.java_permission_service import java_permission_service
from backend.common.log import log


class JavaPermissionMiddleware(BaseHTTPMiddleware):
    """Java系统权限控制中间件"""
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        """
        初始化权限中间件
        
        :param app: FastAPI应用实例
        :param exclude_paths: 排除权限检查的路径列表
        """
        super().__init__(app)
        
        # 默认排除的路径
        self.exclude_paths = exclude_paths or [
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/iot/v1/knowledge-base/health",  # 健康检查接口
        ]
        
        # 权限路径映射 (API路径 -> 权限代码)
        self.permission_mapping = {
            # 知识库相关权限
            "/api/iot/v1/knowledge-base/list": "knowledge:base:list",
            "/api/iot/v1/knowledge-base/create": "knowledge:base:create", 
            "/api/iot/v1/knowledge-base/update": "knowledge:base:update",
            "/api/iot/v1/knowledge-base/delete": "knowledge:base:delete",
            "/api/iot/v1/knowledge-base/stats": "knowledge:base:stats",
            
            # 系统管理权限
            "/api/v1/sys/users": "system:user:list",
            "/api/v1/sys/roles": "system:role:list",
            "/api/v1/sys/menus": "system:menu:list",
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        权限检查中间件处理逻辑
        
        :param request: HTTP请求
        :param call_next: 下一个中间件或路由处理器
        :return: HTTP响应
        """
        try:
            # 检查是否需要权限验证
            if not self._need_permission_check(request):
                return await call_next(request)
            
            # 获取用户ID (从认证中间件设置的用户信息中获取)
            user_id = self._get_user_id_from_request(request)
            if not user_id:
                log.warning(f"权限检查失败: 未找到用户ID, path={request.url.path}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户认证信息"
                )
            
            # 获取所需权限
            required_permission = self._get_required_permission(request)
            if not required_permission:
                # 没有配置权限要求，允许通过
                return await call_next(request)
            
            # 检查用户权限
            has_permission = await java_permission_service.check_user_permission(
                user_id, required_permission
            )
            
            if not has_permission:
                log.warning(f"权限检查失败: user_id={user_id}, permission={required_permission}, path={request.url.path}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {required_permission}"
                )
            
            log.info(f"权限检查通过: user_id={user_id}, permission={required_permission}, path={request.url.path}")
            
            # 权限检查通过，继续处理请求
            return await call_next(request)
            
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            log.error(f"权限中间件处理异常: {e}, path={request.url.path}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="权限检查服务异常"
            )
    
    def _need_permission_check(self, request: Request) -> bool:
        """
        判断是否需要权限检查
        
        :param request: HTTP请求
        :return: 是否需要权限检查
        """
        path = request.url.path
        
        # 检查排除路径
        for exclude_path in self.exclude_paths:
            if path.startswith(exclude_path):
                return False
        
        # 只对API路径进行权限检查
        if not path.startswith("/api/"):
            return False
        
        return True
    
    def _get_user_id_from_request(self, request: Request) -> Optional[int]:
        """
        从请求中获取用户ID
        
        :param request: HTTP请求
        :return: 用户ID
        """
        try:
            # 从认证中间件设置的用户信息中获取
            if hasattr(request.state, 'user_id'):
                return request.state.user_id
            
            # 从用户对象中获取 (如果有的话)
            if hasattr(request, 'user') and hasattr(request.user, 'id'):
                return request.user.id
            
            # 从Java认证信息中获取
            if hasattr(request.state, 'java_user') and 'userId' in request.state.java_user:
                return request.state.java_user['userId']
            
            return None
            
        except Exception as e:
            log.error(f"获取用户ID失败: {e}")
            return None
    
    def _get_required_permission(self, request: Request) -> Optional[str]:
        """
        获取请求所需的权限代码
        
        :param request: HTTP请求
        :return: 权限代码
        """
        path = request.url.path
        method = request.method
        
        # 精确匹配
        if path in self.permission_mapping:
            return self.permission_mapping[path]
        
        # 模糊匹配 (根据路径模式)
        if path.startswith("/api/iot/v1/knowledge-base/"):
            if method == "GET":
                return "knowledge:base:query"
            elif method == "POST":
                return "knowledge:base:create"
            elif method in ["PUT", "PATCH"]:
                return "knowledge:base:update"
            elif method == "DELETE":
                return "knowledge:base:delete"
        
        # 系统管理相关
        if path.startswith("/api/v1/sys/"):
            if "user" in path:
                return "system:user:query"
            elif "role" in path:
                return "system:role:query"
            elif "menu" in path:
                return "system:menu:query"
        
        # 默认返回None，表示不需要特殊权限
        return None
    
    def add_permission_mapping(self, path: str, permission: str):
        """
        添加权限映射
        
        :param path: API路径
        :param permission: 权限代码
        """
        self.permission_mapping[path] = permission
    
    def remove_permission_mapping(self, path: str):
        """
        移除权限映射
        
        :param path: API路径
        """
        if path in self.permission_mapping:
            del self.permission_mapping[path]


# 创建权限中间件实例
def create_java_permission_middleware(exclude_paths: Optional[list] = None):
    """
    创建Java权限中间件实例
    
    :param exclude_paths: 排除权限检查的路径列表
    :return: 中间件类
    """
    def middleware_factory(app):
        return JavaPermissionMiddleware(app, exclude_paths)
    
    return middleware_factory
