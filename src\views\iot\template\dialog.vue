<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="700">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                        <el-form-item label="模型名称" prop="modelName">
                            <el-input v-model="state.ruleForm.modelName" placeholder="请输入通用物模型标题" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                        <el-form-item label="模型标识" prop="identifier">
                            <el-input v-model="state.ruleForm.identifier" placeholder="请输入标识符，例如:temperature"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                        <el-form-item label="模型排序" prop="modelOrder">
                            <el-input-number v-model="state.ruleForm.modelOrder" controls-position="right"
                                placeholder="请输入排序" class="w100" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                        <el-form-item label="模型类别" prop="modelOrder">
                            <el-radio-group v-model="state.ruleForm.type" @change="typeChange(state.ruleForm.type)">
                                <el-radio-button :value="1">属性</el-radio-button>
                                <el-radio-button :value="2">功能</el-radio-button>
                                <el-radio-button :value="3">事件</el-radio-button>
                                <el-radio-button :value="4">关系</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-form-item label="模型特性" prop="property">
                    <el-row>
                        <el-tooltip effect="dark" content="设备详情中以图表方式展示" placement="top">
                            <el-checkbox name="isChart" label="图表展示" @change="isChartChange"
                                v-show="state.ruleForm.type == 1" v-model="state.ruleForm.isChart" :true-value="1"
                                :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="实时显示监测数据，但是不会存储到数据库" placement="top">
                            <el-checkbox name="isMonitor" label="实时监测" @change="isMonitorChange"
                                v-show="state.ruleForm.type == 1" v-model="state.ruleForm.isMonitor" :true-value="1"
                                :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                            <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly" :true-value="1"
                                :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="设备上报的数据会存储到数据库作为历史数据" placement="top">
                            <el-checkbox name="isHistory" label="历史存储" v-model="state.ruleForm.isHistory"
                                :true-value="1" :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="设备分享时需要指定是否拥有该权限" placement="top">
                            <el-checkbox name="isSharePerm" label="分享权限" v-model="state.ruleForm.isSharePerm"
                                :true-value="1" :false-label="0"></el-checkbox>
                        </el-tooltip>
                    </el-row>
                </el-form-item> -->
                <el-divider />
                <!-- 下方左侧 -->
                <div style="display: flex;min-height: 240px;">
                    <div style="width: 35%; padding: 20px;">
                        <!-- 如果是当前是属性显示下拉框 -->
                        <el-select v-if="state.ruleForm.type == 1" v-model="state.ruleForm.subType" placeholder="请选择属性"
                            clearable size="default">
                            <el-option v-for="dict in sub_type_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="Number(dict.dictValue)" />
                        </el-select>
                        <!-- 属性 -->
                        <el-form-item v-if="state.ruleForm.type == 1" style="margin-top: 20px;" label="模型特性:"
                            prop="property">
                            <el-row v-if="state.ruleForm.subType != 3">
                                <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                                    <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                        :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="" placement="top">
                                    <el-checkbox name="isRequired" label="必填数据" v-model="state.ruleForm.isRequired"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="" placement="top">
                                    <el-checkbox name="isUnique" label="唯一数据" v-model="state.ruleForm.isUnique"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                            </el-row>
                            <el-row v-if="state.ruleForm.subType == 3">
                                <el-tooltip effect="dark" content="设备详情中以图表方式展示" placement="top">
                                    <el-checkbox name="isChart" label="图表展示" @change="isChartChange"
                                        v-show="state.ruleForm.type == 1" v-model="state.ruleForm.isChart"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="实时显示监测数据，但是不会存储到数据库" placement="top">
                                    <el-checkbox name="isMonitor" label="实时监测" @change="isMonitorChange"
                                        v-show="state.ruleForm.type == 1" v-model="state.ruleForm.isMonitor"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                                    <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                        :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="设备上报的数据会存储到数据库作为历史数据" placement="top">
                                    <el-checkbox name="isHistory" label="历史存储" v-model="state.ruleForm.isHistory"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                            </el-row>
                        </el-form-item>
                        <!-- 功能 -->
                        <el-form-item v-if="state.ruleForm.type == 2" style="margin-top: 20px;" label="模型特性:"
                            prop="property">
                            <el-row>
                                <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                                    <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                        :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="设备上报的数据会存储到数据库作为历史数据" placement="top">
                                    <el-checkbox name="isHistory" label="历史存储" v-model="state.ruleForm.isHistory"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                            </el-row>
                        </el-form-item>
                        <!-- 事件 -->
                        <el-form-item v-if="state.ruleForm.type == 3" style="margin-top: 20px;" label="模型特性:"
                            prop="property">
                            <el-row>
                                <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                                    <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                        :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="设备上报的数据会存储到数据库作为历史数据" placement="top">
                                    <el-checkbox name="isHistory" label="历史存储" v-model="state.ruleForm.isHistory"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                            </el-row>
                        </el-form-item>
                        <!-- 关系 -->
                        <el-form-item v-if="state.ruleForm.type == 4" style="margin-top: 20px;" label="模型特性:"
                            prop="property">
                            <el-row>
                                <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                                    <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                        :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                                <el-tooltip effect="dark" content="" placement="top">
                                    <el-checkbox name="isRequired" label="必填数据" v-model="state.ruleForm.isRequired"
                                        :true-value="1" :false-label="0"></el-checkbox>
                                </el-tooltip>
                            </el-row>
                        </el-form-item>
                    </div>
                    <!-- 分割线 -->
                    <div>
                        <div style="background: #dde0e7;height: 100%; width: 1px;" />
                    </div>
                    <div style="width: 65%;padding: 20px; ">
                        <el-form-item label="数据类型" prop="datatype">
                            <el-select v-model="state.ruleForm.datatype" placeholder="请选择数据类型" style="width: 385px">
                                <!-- @change="dataTypeChange" -->
                                <el-option key="integer" label="整数" value="integer"
                                    :disabled="state.ruleForm.type == 4"></el-option>
                                <el-option key="decimal" label="小数" value="decimal"
                                    :disabled="state.ruleForm.type == 4"></el-option>
                                <el-option key="bool" label="布尔" value="bool"
                                    :disabled="state.ruleForm.isChart == 1"></el-option>
                                <el-option key="enum" label="枚举" value="enum"
                                    :disabled="state.ruleForm.isChart == 1"></el-option>
                                <el-option key="string" label="字符串" value="string"
                                    :disabled="state.ruleForm.isChart == 1"></el-option>
                                <el-option key="array" label="数组" value="array"
                                    :disabled="state.ruleForm.isChart == 1 && state.ruleForm.type != 4"></el-option>
                                <el-option key="object" label="对象" value="object"
                                    :disabled="state.ruleForm.isChart == 1"></el-option>
                                <el-option key="file" label="文件" value="file"
                                    :disabled="state.ruleForm.isChart == 1"></el-option>
                            </el-select>
                        </el-form-item>
                        <div v-if="state.ruleForm.datatype == 'integer' || state.ruleForm.datatype == 'decimal'">
                            <el-form-item label="取值范围">
                                <el-row style="justify-content: space-between;">
                                    <el-col :span="9">
                                        <el-input v-model="state.ruleForm.specs.min" placeholder="最小值" type="number" />
                                    </el-col>
                                    <el-col :span="2" align="center">到</el-col>
                                    <el-col :span="9">
                                        <el-input v-model="state.ruleForm.specs.max" placeholder="最大值" type="number" />
                                    </el-col>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="单位">
                                <el-input v-model="state.ruleForm.specs.unit" placeholder="请输入单位，例如：℃"
                                    style="width: 385px" />
                            </el-form-item>
                            <el-form-item label="步长">
                                <el-input-number controls-position="right" v-model="state.ruleForm.specs.step"
                                    placeholder="请输入步长，例如：1" type="number" style="width: 385px" />
                            </el-form-item>
                        </div>
                        <div v-if="state.ruleForm.datatype == 'bool'">
                            <el-form-item label="布尔值" prop="">
                                <el-row style="margin-bottom: 10px">
                                    <el-col :span="9">
                                        <el-input v-model="state.ruleForm.specs.falseText" placeholder="例如：关闭" />
                                    </el-col>
                                    <el-col :span="12" :offset="1">（0 值对应文本）</el-col>
                                </el-row>
                                <el-row>
                                    <el-col :span="9">
                                        <el-input v-model="state.ruleForm.specs.trueText" placeholder="例如：打开" />
                                    </el-col>
                                    <el-col :span="12" :offset="1">（1 值对应文本）</el-col>
                                </el-row>
                            </el-form-item>
                        </div>
                        <div v-if="state.ruleForm.datatype == 'enum'">
                            <el-form-item label="展示方式">
                                <el-select v-model="state.ruleForm.specs.showWay" placeholder="请选择展示方式"
                                    style="width: 175px">
                                    <el-option key="select" label="下拉框" value="select"></el-option>
                                    <el-option key="button" label="按钮" value="button"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="枚举项" prop="">
                                <el-row v-for="(item, index) in state.ruleForm.specs.enumList" :key="'enum' + index"
                                    style="margin-bottom: 10px">
                                    <el-col :span="8">
                                        <el-tooltip effect="dark" content="参数值，例如：0" placement="top">
                                            <el-input v-model="item.value" placeholder="参数值" />
                                        </el-tooltip>
                                    </el-col>
                                    <el-col :span="8" :offset="1">
                                        <el-tooltip effect="dark" content="参数描述，例如：中速档位" placement="top">
                                            <el-input v-model="item.text" placeholder="参数描述" />
                                        </el-tooltip>
                                    </el-col>
                                    <el-col :span="4" :offset="1" v-if="index != 0"><a style="color: #f56c6c"
                                            @click="removeEnumItem(index)">删除</a></el-col>
                                    <el-col :span="4" :offset="1" v-else style="width: 28px;"></el-col>
                                </el-row>
                                <div>
                                    +
                                    <a style="color: #409eff" @click="addEnumItem()">添加枚举项</a>
                                </div>
                            </el-form-item>
                        </div>
                        <div v-if="state.ruleForm.datatype == 'string'">
                            <el-form-item label="最大长度" prop="">
                                <el-row>
                                    <el-col :span="9">
                                        <el-input v-model="state.ruleForm.specs.maxLength" placeholder="例如：1024"
                                            type="number" />
                                    </el-col>
                                    <el-col :span="14" :offset="1">（字符串的最大长度）</el-col>
                                </el-row>
                            </el-form-item>
                        </div>
                        <div v-if="state.ruleForm.datatype == 'array'" style="width: 400px;">
                            <el-form-item label="元素个数" prop="">
                                <el-row>
                                    <el-col :span="9">
                                        <el-input v-model="state.ruleForm.specs.arrayCount" placeholder="例如：5"
                                            type="number" />
                                    </el-col>
                                </el-row>
                            </el-form-item>
                            <el-form-item label="数组类型" prop="">
                                <el-radio-group v-model="state.ruleForm.specs.arrayType">
                                    <el-radio value="integer">整数</el-radio>
                                    <el-radio value="decimal">小数</el-radio>
                                    <el-radio value="string">字符串</el-radio>
                                    <el-radio v-if="state.ruleForm.type != 4" value="object">对象</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="对象参数" v-if="state.ruleForm.specs.arrayType == 'object'"
                                label-position="top" style="margin-left: 20px;">
                                <div style=" border-radius: 5px;">
                                    <div style="padding: 0 10px 5px 0; width: 390px;align-items: center;  display: flex;"
                                        v-for="(item, index) in state.ruleForm.specs.params" :key="index">
                                        <div style="margin-top: 5px" v-if="index == 0"></div>
                                        <div>
                                            <el-input readonly v-model="item.name" size="default" placeholder="请选择设备"
                                                style="margin-top: 3px">
                                                <template #prepend>
                                                    <el-tag size="default" effect="dark"
                                                        style="margin:0 5px 0 -14px; width: 5px;">{{
                                                            item.order }}</el-tag>
                                                    <el-tooltip effect="dark"
                                                        :content="state.ruleForm.identifier + '_' + item.id"
                                                        placement="top">
                                                        <div class="scrollable-div">{{ state.ruleForm.identifier +
                                                            '_' + item.id }}</div>
                                                    </el-tooltip>

                                                </template>
                                                <template #append>
                                                    <el-button @click="editParameter(item, index)"
                                                        size="default">编辑</el-button>
                                                </template>
                                            </el-input>
                                        </div>
                                        <div>
                                            <el-button size="default" plain type="danger"
                                                style="padding: 5px;margin-left: 10px;"
                                                @click="removeParameter(index)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    +
                                    <a style="color: #409eff; cursor: pointer;" @click="addParameter()">添加参数</a>
                                </div>
                            </el-form-item>
                        </div>
                        <div v-if="state.ruleForm.datatype == 'object'" style="margin-left: 20px;">
                            <el-form-item label="对象参数" label-position="top">
                                <div style="width: 390px;">
                                    <div style="padding: 0 10px 5px 0;width: 390px;align-items: center;  display: flex;"
                                        v-for="(item, index) in state.ruleForm.specs.params" :key="index">
                                        <div v-if="index == 0"></div>
                                        <div>
                                            <el-input readonly v-model="item.name" size="default" placeholder="请选择设备"
                                                style="margin-top: 3px">
                                                <template #prepend>
                                                    <el-tag size="default" effect="dark"
                                                        style="margin:0 5px 0 -14px; width: 5px;">{{
                                                            item.order }}</el-tag>
                                                    <el-tooltip effect="dark"
                                                        :content="state.ruleForm.identifier + '_' + item.id"
                                                        placement="top">
                                                        <div class="scrollable-div">{{ state.ruleForm.identifier + '_' +
                                                            item.id }}</div>
                                                    </el-tooltip>

                                                </template>
                                                <template #append>
                                                    <!-- 编辑 -->
                                                    <el-button @click="editParameter(item, index)">编辑</el-button>
                                                </template> </el-input>
                                        </div>
                                        <div>
                                            <el-button size="default" plain type="danger"
                                                style="padding: 5px;margin-left: 10px;"
                                                @click="removeParameter(index)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    +
                                    <a style="color: #409eff; cursor: pointer;" @click="addParameter()">添加参数</a>
                                </div>
                            </el-form-item>
                        </div>
                        <div v-if="state.ruleForm.datatype == 'file'" style="margin-left: 20px;">
                            <!-- <el-form-item label="文件" label-width="70px">
                                <imageUpload 
                                    ref="image-upload" v-model:model-value="state.ruleForm.specs.imgUrl"
                                    :limit="3" :fileSize="1">
                                </imageUpload>
                                <el-upload ref="uploadRef" :limit="3" accept=".xlsx, .xls" :headers="upload.headers"
                                    :action="upload.url + '?updateSupport=' + upload.updateSupport"
                                    :disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
                                    :on-success="handleFileSuccess" :auto-upload="false" drag>
                                    <i><el-icon :size="30" color="#c0c4cc"><ele-UploadFilled /></el-icon></i>
                                    <div style="font-size: 13px;" class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                </el-upload>
                                <div style="text-align: center;" slot="tip">
                                    <span class="el-upload__tip">仅允许导入xls、xlsx格式文件。</span>
                                    <el-link type="primary" underline="never"
                                        style="font-size:12px;vertical-align: baseline;"
                                        @click="importTemplate">下载模板</el-link>
                                </div>
                                <el-button type="primary" @click="submitFileForm">确 定</el-button>
                                <fileUpload ref="file-upload" :value="form.filePath" :limit="1" :fileSize="10"
                                    :fileType="['bin', 'zip', 'pdf']" @input="getFilePath($event)"></fileUpload>
                            </el-form-item> -->
                        </div>
                    </div>
                </div>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
        <!--物模型参数类型-->
        <thingsParameter v-model="paramData" @dataEvent="getParamData($event)" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox, ElUpload, FormInstance } from 'element-plus';
import { useUserInfo } from '/@/stores/userInfo';
import { addTemplate, getTemplate, updateTemplate } from '/@/api/iot/template';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { Session } from '/@/utils/storage';
import imageUpload from '/@/components/ImageUpload/index.vue'
import { download } from '/@/utils/request';
const dictStore = useDictStore();  // 使用 Pinia store
const thingsParameter = defineAsyncComponent(() => import('./parameter.vue'));

// import thingsParameter from './parameter.vue';
const userInfoStore = useUserInfo();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        modelName: '', // 通用物模型标题
        identifier: '', // 通用物模型类型
        modelOrder: 0,
        modelId: '',
        type: 1 as any,
        isChart: 1,//图表展示
        isMonitor: 1 as any,//实时监测
        isReadonly: 1 as any,//只读数据
        isHistory: 1 as any,//历史存储
        isRequired: 1 as any,//必填数据
        isUnique: 1 as any, //
        // isSharePerm: 1 as any,
        datatype: 'integer' as any,
        subType: 1,//属性
        specs: {
            enumList: [
                {
                    value: '',
                    text: '',
                },
            ],
            arrayType: 'integer',
            arrayCount: 5,
            showWay: 'select', // 显示方式select=下拉选择框，button=按钮
            params: [] as any,
            min: '',
            max: '',
            unit: '',
            step: undefined,
            falseText: '',
            trueText: '',
            maxLength: '',
            imgUrl: ''
        },
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const sub_type_list = ref<TypeOption[]>([]);
// 对象类型参数
let paramData = reactive({
    index: null as any,
    parameter: {},
    componentKey: 0
})
// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});
// 上传文件数据
const upload = reactive({
    title: '',
    isUploading: false,
    updateSupport: false,
    headers: {
        Authorization: `Bearer ${Session.get('token')}`,
    },
    url: `${import.meta.env.VITE_API_URL}iot/tool/upload`,
});
const uploadRef = ref<InstanceType<typeof ElUpload> | null>(null);
// 校验规则
const rules = reactive({
    modelName: [
        {
            required: true,
            message: '物模型名称不能为空',
            trigger: 'blur',
        },
    ],
    identifier: [
        {
            required: true,
            message: '标识符，产品下唯一不能为空',
            trigger: 'blur',
        },
    ],
    modelOrder: [
        {
            required: true,
            message: '模型排序不能为空',
            trigger: 'blur',
        },
    ],
    type: [
        {
            required: true,
            message: '模型类别不能为空',
            trigger: 'change',
        },
    ],
    datatype: [
        {
            required: true,
            message: '数据类型不能为空',
            trigger: 'change',
        },
    ],

})
// 打开弹窗
const openDialog = (type: string, row: any, modelId: string) => {
    if (type === 'edit') {
        getTemplate(row.modelId).then(response => {
            let tempForm = response.data.data;
            // Json转对象
            tempForm.specs = JSON.parse(tempForm.specs);
            if (!tempForm.specs.enumList) {
                tempForm.specs.showWay = 'select';
                tempForm.specs.enumList = [
                    {
                        value: '',
                        text: '',
                    },
                ];
            }
            if (!tempForm.specs.arrayType) {
                tempForm.specs.arrayType = 'integer';
            }
            if (!tempForm.specs.arrayCount) {
                tempForm.specs.arrayCount = 5;
            }
            if (!tempForm.specs.params) {
                tempForm.specs.params = [];
            }
            // 对象和数组中参数删除前缀
            if ((tempForm.specs.type == 'array' && tempForm.specs.arrayType == 'object') || tempForm.specs.type == 'object') {
                for (let i = 0; i < tempForm.specs.params.length; i++) {
                    tempForm.specs.params[i].id = String(tempForm.specs.params[i].id).substring(String(tempForm.identifier).length + 1);
                }
            }
            state.ruleForm = tempForm;

        });
        state.dialog.title = '修改通用物模型';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '新增通用物模型';
        state.dialog.submitTxt = '新 增';
    }
    getdictdata()
    state.dialog.isShowDialog = true;
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 类型改变
const typeChange = (type: any) => {
    console.log(type);
    if (type == 1) {
        state.ruleForm.isChart = 1 as any;
        state.ruleForm.isHistory = 1;
        state.ruleForm.isMonitor = 1;
        state.ruleForm.isReadonly = 1;
        // state.ruleForm.isSharePerm = 1;
        state.ruleForm.datatype = 'integer';
    } else if (type == 2) {
        state.ruleForm.isChart = 0;
        state.ruleForm.isHistory = 1;
        // state.ruleForm.isSharePerm = 1;
        state.ruleForm.isMonitor = 0;
        state.ruleForm.isReadonly = 0;
        state.ruleForm.datatype = 'integer';
    } else if (type == 3) {
        state.ruleForm.isChart = 0;
        state.ruleForm.isHistory = 1;
        state.ruleForm.isMonitor = 0;
        state.ruleForm.isReadonly = 1;
        // state.ruleForm.isSharePerm = 0;
        state.ruleForm.datatype = 'integer';
    } else {
        state.ruleForm.datatype = 'array';
    }
}
// 是否图表展示改变
const isChartChange = () => {
    if (state.ruleForm.isChart == 1) {
        state.ruleForm.isReadonly = 1;
    } else {
        state.ruleForm.isMonitor = 0;
    }
}
// 是否实时监测改变
const isMonitorChange = () => {
    if (state.ruleForm.isMonitor == 1) {
        state.ruleForm.isReadonly = 1;
        state.ruleForm.isChart = 1;
    }
}
// 是否只读数据改变
const isReadonlyChange = () => {
    if (state.ruleForm.isReadonly == 0) {
        state.ruleForm.isMonitor = 0;
        state.ruleForm.isChart = 0;
    }
}

// 格式化物模型
const formatThingsSpecs = () => {
    var data = {} as any;
    data.type = state.ruleForm.datatype;
    if (state.ruleForm.datatype == 'integer' || state.ruleForm.datatype == 'decimal') {
        data.min = Number(state.ruleForm.specs.min ? state.ruleForm.specs.min : 0);
        data.max = Number(state.ruleForm.specs.max ? state.ruleForm.specs.max : 100);
        data.unit = state.ruleForm.specs.unit ? state.ruleForm.specs.unit : '';
        data.step = Number(state.ruleForm.specs.step ? state.ruleForm.specs.step : 1);
    } else if (state.ruleForm.datatype == 'string') {
        data.maxLength = Number(state.ruleForm.specs.maxLength ? state.ruleForm.specs.maxLength : 1024);
    } else if (state.ruleForm.datatype == 'bool') {
        data.falseText = state.ruleForm.specs.falseText ? state.ruleForm.specs.falseText : '关闭';
        data.trueText = state.ruleForm.specs.trueText ? state.ruleForm.specs.trueText : '打开';
    } else if (state.ruleForm.datatype == 'enum') {
        data.showWay = state.ruleForm.specs.showWay;
        if (state.ruleForm.specs.enumList && state.ruleForm.specs.enumList[0].text != '') {
            data.enumList = state.ruleForm.specs.enumList;
        } else {
            data.showWay = 'select';
            data.enumList = [
                {
                    value: '0',
                    text: '低',
                },
                {
                    value: '1',
                    text: '高',
                },
            ];
        }
    } else if (state.ruleForm.datatype == 'array') {
        data.arrayType = state.ruleForm.specs.arrayType;
        data.arrayCount = state.ruleForm.specs.arrayCount ? state.ruleForm.specs.arrayCount : 5;
        if (data.arrayType == 'object') {
            data.params = state.ruleForm.specs.params;
            // 物模型名称作为参数的标识符前缀
            for (let i = 0; i < data.params.length; i++) {
                data.params[i].id = state.ruleForm.identifier + '_' + data.params[i].id;
            }
        }
    } else if (state.ruleForm.datatype == 'object') {
        data.params = state.ruleForm.specs.params;
        // 物模型名称作为参数的标识符前缀
        for (let i = 0; i < data.params.length; i++) {
            data.params[i].id = state.ruleForm.identifier + '_' + data.params[i].id;
        }
    }
    return JSON.stringify(data);
}
/** 数据类型改变 */
const dataTypeChange = () => { }
/** 添加枚举项 */
const addEnumItem = () => {
    state.ruleForm.specs.enumList.push({
        value: '',
        text: '',
    });
}
/** 删除枚举项 */
const removeEnumItem = (index: any) => {
    state.ruleForm.specs.enumList.splice(index, 1);
}
/** 添加参数 */
const addParameter = () => {
    paramData.index = -1 as number;
    paramData.parameter = {};
    paramData.componentKey += 1;
}
/** 编辑参数*/
const editParameter = (data: any, index: any) => {
    if (paramData.index !== index) { // 确保值的变化
        paramData.index = index;   // 更新值
    }
    paramData.parameter = data;
    paramData.componentKey += 1;
}
/** 删除动作 */
const removeParameter = (index: any) => {
    state.ruleForm.specs.params.splice(index, 1);
}
/**获取设置的参数对象*/
const getParamData = (data: { index: number; parameter: any; }) => {
    console.log(data, 'data');

    if (data.index == -1) {
        state.ruleForm.specs.params.push(data.parameter);
    } else {
        state.ruleForm.specs.params[data.index] = data.parameter;
        // 解决数组在界面中不更新问题
        // this.$set(state.ruleForm.specs.params, data.index, state.ruleForm.specs.params[data.index]);
        state.ruleForm.specs.params[data.index] = state.ruleForm.specs.params[data.index];

    }
}
const containsUnderscore = (value: string) => {
    // 使用正则表达式检查值中是否包含下划线
    return /_/.test(value);
}
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            // 验证对象或对象数组中的参数不能为空
            if (state.ruleForm.datatype == 'object' || (state.ruleForm.datatype == 'array' && state.ruleForm.specs.arrayType == 'object')) {
                if (!state.ruleForm.specs.params || state.ruleForm.specs.params == 0) {
                    ElMessage.error('对象的参数不能为空');
                    return;
                }
                if (containsUnderscore(state.ruleForm.identifier)) {
                    ElMessage.error('对象类型模型标识输入不能包含下划线，请重新填写模型标识！');
                    return;
                }
            }
            // 验证对象参数标识符不能相同
            if (state.ruleForm.specs.params && state.ruleForm.specs.params.length > 0) {
                let arr = state.ruleForm.specs.params.map((item: { id: any; }) => item.id).sort();
                for (let i = 0; i < arr.length; i++) {
                    if (arr[i] == arr[i + 1]) {
                        ElMessage.error('参数标识 ' + arr[i] + ' 重复');
                        return;
                    }
                }
            }
            //验证模型特性为图表展示时，数据类型是否为整数或者小数
            if ((state.ruleForm.isChart == 1 && state.ruleForm.datatype != 'integer') && (state.ruleForm.isChart == 1 && state.ruleForm.datatype != 'decimal') && (state.ruleForm.type != 4)) {
                ElMessage.error('请重新选择数据类型！');
            }
            else if (state.ruleForm.modelId != '') {
                // 格式化specs
                let tempForm = JSON.parse(JSON.stringify(state.ruleForm));
                tempForm.specs = formatThingsSpecs();
                if (state.ruleForm.type == 2) {
                    tempForm.isMonitor = 0;
                    tempForm.isChart = 0;
                } else if (state.ruleForm.type == 3) {
                    tempForm.isMonitor = 0;
                    tempForm.isChart = 0;
                }
                // 添加通用物模型的修改者
                tempForm.updateBy = userInfoStore.userInfos.username
                if (tempForm.type != 1) {
                    tempForm.subType == 1
                }
                updateTemplate(tempForm).then((response) => {
                    ElMessage.success('修改成功');
                    emit('refresh');
                    closeDialog();
                });
            } else {
                // 格式化specs
                let tempForm = JSON.parse(JSON.stringify(state.ruleForm));
                tempForm.specs = formatThingsSpecs();
                if (state.ruleForm.type == 2) {
                    tempForm.isMonitor = 0;
                } else if (state.ruleForm.type == 3) {
                    tempForm.isMonitor = 0;
                    tempForm.isChart = 0;
                }
                // 添加通用物模型的创造者
                tempForm.createBy = userInfoStore.userInfos.username
                if (tempForm.type != 1) {
                    tempForm.subType == 1
                }
                console.log(tempForm, 'tempForm');
                // return
                addTemplate(tempForm).then((response) => {
                    ElMessage.success('新增成功');
                    emit('refresh');
                    closeDialog();

                });
            }
        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据 查询设备分组列表
const getdictdata = async () => {
    try {
        sub_type_list.value = await dictStore.fetchDict('iot_things_sub_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
<style scoped>
.scrollable-div {
    width: 64px;
    /* 固定宽度 */
    overflow: hidden;
    /* 默认不显示滚动条 */
}

:deep(.el-upload-dragger) {
    padding: 20px 10px;
}

.scrollable-div:hover {
    /* overflow: auto; */
    /* 鼠标悬停时显示滚动条 */
}
</style>
