#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Java /getRouters接口
"""

import asyncio
import sys
import json
sys.path.append('.')

async def test_java_getrouters_api():
    print('=== 测试Java /getRouters接口 ===')
    
    import aiohttp
    
    # 使用pythontest用户的JWT token
    token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImJkYWNhNDY5LTFjMzAtNDk0Zi1iMDRlLThhOTc1NGRjM2MyNiJ9.leue4Ni75SoSBDCe6y2VO2XGKOEIvISYBSBvh6_yiQn6DnSnmoI_a3cabnj7A6iUXLGOpmLa0JtImdNCJxhIWw'
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # 调用本地前端代理的getRouters接口
            async with session.get(
                'http://localhost/prod-api/getRouters',
                headers=headers
            ) as response:
                print(f'响应状态码: {response.status}')
                
                if response.status == 200:
                    data = await response.json()
                    print(f'响应数据结构: {type(data)}')
                    
                    if 'data' in data:
                        menus = data['data']
                        print(f'菜单数量: {len(menus)}')
                        
                        # 查找设备管理和通用物模型相关菜单
                        print('\n=== 设备管理相关菜单 ===')
                        for menu in menus:
                            menu_name = menu.get('name', '')
                            menu_path = menu.get('path', '')
                            menu_perms = menu.get('perms', '无')
                            
                            if '设备' in menu_name or 'iot' in menu_path:
                                print(f'菜单: {menu_name} | 路径: {menu_path} | 权限: {menu_perms}')
                                
                                # 检查子菜单
                                if 'children' in menu:
                                    for child in menu['children']:
                                        child_name = child.get('name', '')
                                        child_path = child.get('path', '')
                                        child_perms = child.get('perms', '无')
                                        
                                        if '物模型' in child_name or 'template' in child_path:
                                            print(f'  子菜单: {child_name} | 路径: {child_path} | 权限: {child_perms}')
                        
                        # 查找知识库相关菜单
                        print('\n=== 知识库相关菜单 ===')
                        for menu in menus:
                            menu_name = menu.get('name', '')
                            menu_path = menu.get('path', '')
                            menu_perms = menu.get('perms', '无')
                            
                            if '知识库' in menu_name or 'kb' in menu_path:
                                print(f'菜单: {menu_name} | 路径: {menu_path} | 权限: {menu_perms}')
                                
                                if 'children' in menu:
                                    for child in menu['children']:
                                        child_name = child.get('name', '')
                                        child_path = child.get('path', '')
                                        child_perms = child.get('perms', '无')
                                        print(f'  子菜单: {child_name} | 路径: {child_path} | 权限: {child_perms}')
                        
                        # 显示所有菜单的概览
                        print('\n=== 所有菜单概览 ===')
                        for i, menu in enumerate(menus, 1):
                            menu_name = menu.get('name', '')
                            menu_path = menu.get('path', '')
                            print(f'{i:2d}. {menu_name} ({menu_path})')
                            
                    else:
                        print('响应中没有data字段')
                        print(f'完整响应: {data}')
                else:
                    error_text = await response.text()
                    print(f'请求失败: {error_text}')
                    
    except Exception as e:
        print(f'请求异常: {e}')

if __name__ == '__main__':
    asyncio.run(test_java_getrouters_api())
