<template>
    <div class="layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="任务名称">
                        <el-input v-model="state.tableData.param.jobName" aria-label="First Name" placeholder="请输入任务名称"
                            style="width: 240px" clearable />
                    </el-form-item>
                    <el-form-item label="任务组名">
                        <el-select v-model="state.tableData.param.jobGroup" placeholder="请选择任务组名" clearable
                            style="width: 240px">
                            <el-option v-for="dict in job_group_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="state.tableData.param.status" placeholder="请选择任务状态" clearable
                            style="width: 240px">
                            <el-option v-for="dict in job_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="handleQuery">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['monitor:job:add']" size="default" type="primary" class="ml5"
                            @click="handleAdd('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['monitor:job:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="handleUpdate('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['monitor:job:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="handleDelete">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['monitor:job:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                        <el-button v-auths="['monitor:job:query']" size="default" type="info" class="ml10"
                            @click="handleJobLog">
                            <el-icon><ele-Operation /></el-icon>
                            日志
                        </el-button>

                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="任务编号" width="100" align="center" prop="jobId" />
                <el-table-column label="任务名称" align="center" prop="jobName" :show-overflow-tooltip="true" />
                <el-table-column label="任务组名" align="center" prop="jobGroup">
                    <template #default="scope">
                        <dictTag :options="job_group_list" :value="scope.row.jobGroup" />
                    </template>
                </el-table-column>
                <el-table-column label="调用目标字符串" align="center" prop="invokeTarget" :show-overflow-tooltip="true" />
                <el-table-column label="cron执行表达式" align="center" prop="cronExpression" :show-overflow-tooltip="true" />
                <el-table-column prop="status" label="状态" align="center" show-overflow-tooltip width="150">
                    <template #default="scope">
                        <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
                            @change="handleStatusChange(scope.row)">
                        </el-switch>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="220" align="center">
                    <template #default="scope">
                        <div>
                            <el-button v-auths="['monitor:job:edit']" class="ml15" text type="primary"
                                @click="handleUpdate('edit', scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
                            <el-button v-auths="['monitor:job:remove']" class="ml15" text type="primary"
                                @click="handleDelete(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                            <el-dropdown v-auths="['monitor:job:changeStatus', 'monitor:job:query']"
                                @command="(command: any) => handleCommand(command, scope.row)">
                                <el-button text type="primary"
                                    class="ml15"><el-icon><ele-DArrowRight /></el-icon>更多</el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <div v-auths="['monitor:job:run']">
                                            <el-dropdown-item
                                                command="handleRun"><el-icon><ele-CaretRight /></el-icon>执行一次</el-dropdown-item>
                                        </div>
                                        <div v-auths="['monitor:job:query']">
                                            <el-dropdown-item
                                                command="handleView"><el-icon><ele-View /></el-icon>任务详细</el-dropdown-item>
                                        </div>
                                        <div v-auths="['monitor:job:query']">
                                            <el-dropdown-item
                                                command="handleJobLog"><el-icon><ele-Operation /></el-icon>调度日志</el-dropdown-item>
                                        </div>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 任务日志详细 -->
            <el-dialog title="任务详细" style="position: absolute; top: 100px;" v-model="jobdata.dialog.openView"
                width="700px" append-to-body>
                <el-form ref="form" :model="jobdata.ruleForm" label-width="120px" size="default">
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="任务编号：">{{ jobdata.ruleForm.jobId }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="任务分组：">{{ jobGroupFormat(jobdata.ruleForm) }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="任务名称：">{{ jobdata.ruleForm.jobName }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="创建时间：">{{ jobdata.ruleForm.createTime }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="cron表达式：">{{ jobdata.ruleForm.cronExpression }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="下次执行时间：">{{ parseTime(jobdata.ruleForm.nextValidTime) }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="调用目标方法：">{{ jobdata.ruleForm.invokeTarget }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="任务状态：">
                                <div v-if="jobdata.ruleForm.status == 0">正常</div>
                                <div v-else-if="jobdata.ruleForm.status == 1">失败</div>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="是否并发：">
                                <div v-if="jobdata.ruleForm.concurrent == 0">允许</div>
                                <div v-else-if="jobdata.ruleForm.concurrent == 1">禁止</div>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="执行策略：">
                                <div v-if="jobdata.ruleForm.misfirePolicy == 0">默认策略</div>
                                <div v-else-if="jobdata.ruleForm.misfirePolicy == 1">立即执行</div>
                                <div v-else-if="jobdata.ruleForm.misfirePolicy == 2">执行一次</div>
                                <div v-else-if="jobdata.ruleForm.misfirePolicy == 3">放弃执行</div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="jobdata.dialog.openView = false">关 闭</el-button>
                    </span>
                </template>
            </el-dialog>
            <!-- 用户导入对话框 -->
            <!-- <el-dialog :title="upload.title" v-model="dialogVisible" width="400px" append-to-body
                style="position: absolute; top: 150px">
                <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
                    :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
                    :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
                    <i><el-icon :size="70" color="#c0c4cc"><ele-UploadFilled /></el-icon></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                </el-upload>
                <div style="text-align: center;" slot="tip">
                    <div class="el-upload__tip" slot="tip">
                        <el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
                    </div>
                    <span class="el-upload__tip">仅允许导入xls、xlsx格式文件。</span>
                    <el-link type="primary" underline="never" style="font-size:12px;vertical-align: baseline;"
                        @click="importTemplate">下载模板</el-link>
                </div>
                <div slot="footer" style="display: flex; justify-content: flex-end;margin-top: 20px;">
                    <el-button type="primary" @click="submitFileForm">确 定</el-button>
                    <el-button @click="dialogVisible = false">取 消</el-button>
                </div>
            </el-dialog> -->
            <!-- 分页 -->
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <JobDialog ref="JobDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="">

import { defineAsyncComponent, reactive, onMounted, ref, toRefs, watch } from 'vue';
import { ElMessageBox, ElMessage, ElUpload } from 'element-plus';
import { download } from '/@/utils/request';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { changeJobStatus, delJob, getJob, listJob, runJob } from '/@/api/monitor/job';
import { parseTime } from '/@/utils/next'
import router from '/@/router';
const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const JobDialog = defineAsyncComponent(() => import('/@/views/monitor/job/dialog.vue'));
interface statusOption {
    dictValue: string;
    dictLabel: string;
}


// 定义变量内容
const JobDialogRef = ref();
const state = reactive<SysUserState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            jobName: undefined,
            jobGroup: undefined,
            status: undefined,
            jobId: undefined
        },
    },
});
const job_group_list = ref<statusOption[]>([]);
const job_status_list = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //jobId
const jobdata = reactive({
    ruleForm: {
        jobName: '' as any,
        jobId: '' as any,
        jobGroup: '' as any,
        cronExpression: '' as any,
        invokeTarget: '' as any,
        concurrent: 1 as any,
        status: '' as any,
        misfirePolicy: 1 as any,
        createTime: '' as any,
        nextValidTime: '' as any
    },
    dialog: {
        openView: false,
    },
})
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listJob(state.tableData.param)
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
// 获取状态数据
const getdictdata = async () => {
    try {
        job_group_list.value = await dictStore.fetchDict('sys_job_group')
        job_status_list.value = await dictStore.fetchDict('sys_job_status')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { jobId: string; }) => item.jobId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getTableData();
}
// 重置按钮
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        jobName: undefined,
        jobGroup: undefined,
        status: undefined,
        jobId: undefined
    }
    getTableData();
}
// 更改用户状态
const handleStatusChange = (row: any) => {
    let text = row.status === "0" ? "启用" : "停用";
    ElMessageBox.confirm('确认要"' + text + '""' + row.jobName + '"任务吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            return changeJobStatus(row.jobId, row.status).then(() => {
                getTableData();
                ElMessage.success(text + "成功");
            });
        })
        .catch((err) => {
            row.status = row.status === "0" ? "1" : "0";
        });
};
// 更多操作触发
const handleCommand = (command: any, row: any) => {
    switch (command) {
        case "handleRun":
            handleRun(row);
            break;
        case "handleView":
            handleView(row);
            break;
        case "handleJobLog":
            handleJobLog(row);
            break;
        default:
            break;
    }
}
// 任务组名字典翻译
const jobGroupFormat = (row: { jobGroup: any; }) => {
    let aaa = ''
    job_group_list.value.forEach(item => {
        if (item.dictValue == row.jobGroup) {
            aaa = item.dictLabel
        }
    })


    return aaa
}
/* 立即执行一次 */
const handleRun = (row: any) => {
    ElMessageBox.confirm('确认要立即执行一次"' + row.jobName + '"任务吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            return runJob(row.jobId, row.jobGroup).then(() => {
                ElMessage.success("执行成功");
            });
        })
        .catch((err) => {
        });
}
/** 任务详细信息 */
const handleView = (row: any) => {
    jobdata.dialog.openView = true;
    getJob(row.jobId).then(response => {
        jobdata.ruleForm = response.data.data;

    });
}
/** 任务日志列表查询 */
const handleJobLog = (row: any) => {
    const jobId = row.jobId || 0;
    router.push('/monitor/job-log/index/' + jobId)
}

// 打开新增用户弹窗
const handleAdd = (type: string) => {
    JobDialogRef.value.openDialog(type);
};
// 打开修改用户弹窗
const handleUpdate = (type: string, row: JobType | undefined) => {
    // this.reset();
    var jobId = ''
    if (!row) {
        jobId = ids.value
    } else {
        jobId = row.jobId
    }
    JobDialogRef.value.openDialog(type, row, jobId);
};
// 删除用户
const handleDelete = (row: JobType) => {
    const jobIds = row.jobId || ids.value;
    ElMessageBox.confirm('是否确认删除用户编号为"' + jobIds + '"的数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delJob(jobIds).then(res => {
                ElMessage.success('删除成功');
                getTableData();
            })

        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('monitor/job/export', {
        ...state.tableData.param
    }, `job_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
}
// 页面加载时
onMounted(() => {
    getTableData()
    getdictdata()
});
</script>

<style scoped lang="scss"></style>
