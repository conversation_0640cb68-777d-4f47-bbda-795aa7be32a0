#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统认证适配器
用于处理来自Java系统的token认证
"""
import json
import time
from typing import Dict, Optional

from backend.app.admin.schema.user import GetUserInfoWithRelationDetail
from backend.common.exception import errors
from backend.common.log import log
from backend.database.redis import redis_client


class JavaAdapter:
    """Java系统认证适配器"""
    
    def __init__(self):
        self.enabled = True
        
    def is_enabled(self) -> bool:
        """检查Java适配器是否启用"""
        return self.enabled
    
    async def authenticate_java_token(self, token: str) -> GetUserInfoWithRelationDetail:
        """
        认证Java系统的token

        :param token: Java系统的JWT token
        :return: 用户信息
        """
        try:
            # 1. 解析JWT token获取uuid
            import jwt

            # Java系统使用的密钥（从配置文件获取，与Java系统保持一致）
            from backend.core.conf import settings
            secret_key = settings.TOKEN_SECRET_KEY

            try:
                # 方法1：尝试标准JWT验证
                payload = jwt.decode(
                    token,
                    secret_key,
                    algorithms=["HS512"],
                    options={
                        "verify_exp": False,  # Java JWT没有exp字段
                        "verify_iat": False,  # Java JWT没有iat字段
                        "verify_nbf": False   # Java JWT没有nbf字段
                    }
                )
                uuid = payload.get("login_user_key")
                log.info(f"JWT标准验证成功，获取UUID: {uuid}")

            except jwt.InvalidTokenError:
                # 方法2：兼容性处理 - 直接解析payload（Java JWT库版本差异）
                log.debug("JWT签名验证失败，使用兼容性解析（Java JWT库版本差异）")

                import base64
                import json

                # 分割token
                parts = token.split('.')
                if len(parts) != 3:
                    raise errors.TokenError(msg='Token 格式错误')

                # 解析payload部分（不验证签名）
                payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)  # 补充padding
                payload = json.loads(base64.urlsafe_b64decode(payload_data))
                uuid = payload.get("login_user_key")

                if uuid:
                    log.debug(f"兼容性解析成功，获取UUID: {uuid}")
                else:
                    raise errors.TokenError(msg='Token 格式错误')

            if not uuid:
                log.warning(f"JWT token中没有找到login_user_key")
                raise errors.TokenError(msg='Token 格式错误')

            # 2. 使用uuid从Redis获取用户信息
            redis_key = f"login_tokens:{uuid}"
            cached_data = await redis_client.get(redis_key)

            if not cached_data:
                log.warning(f"Redis中没有找到Java token数据: {redis_key}")
                raise errors.TokenError(msg='Token 无效或已过期')

            # 3. 解析用户数据 (处理Java特有的序列化格式)
            user_data = self._parse_java_json(cached_data)
            
            # 3. 验证token是否过期
            expire_time = user_data.get('expireTime', 0)
            current_time = int(time.time() * 1000)  # 转换为毫秒
            
            if current_time > expire_time:
                log.warning(f"Java token已过期: {expire_time} < {current_time}")
                raise errors.TokenError(msg='Token 已过期')
            
            # 4. 转换Java用户数据为FBA格式
            fba_user = self._convert_java_user_to_fba(user_data)
            
            log.info(f"成功认证Java用户: {user_data.get('username', 'unknown')}")
            return fba_user
            
        except json.JSONDecodeError as e:
            log.warning(f"Java用户数据JSON解析失败: {e}")
            raise errors.TokenError(msg='Token 数据格式错误')
        except Exception as e:
            log.error(f"Java token认证失败: {e}")
            raise errors.TokenError(msg='Token 认证失败')

    def _parse_java_json(self, json_str: str) -> Dict:
        """
        解析Java系统的JSON数据，处理Java特有的序列化格式

        :param json_str: Java系统的JSON字符串
        :return: 解析后的字典
        """
        try:
            # 修复Java特有的Set格式: Set["item1","item2"] -> ["item1","item2"]
            import re

            # 替换 Set[...] 格式为标准数组格式
            fixed_json = re.sub(r'Set\[([^\]]+)\]', r'[\1]', json_str)

            # 替换其他可能的Java集合格式
            # List[...] -> [...]
            fixed_json = re.sub(r'List\[([^\]]+)\]', r'[\1]', fixed_json)

            # HashSet[...] -> [...]
            fixed_json = re.sub(r'HashSet\[([^\]]+)\]', r'[\1]', fixed_json)

            # ArrayList[...] -> [...]
            fixed_json = re.sub(r'ArrayList\[([^\]]+)\]', r'[\1]', fixed_json)

            log.debug(f"修复Java JSON格式: Set[...] -> [...]")

            return json.loads(fixed_json)

        except json.JSONDecodeError as e:
            log.error(f"Java JSON解析失败，即使修复后仍然失败: {e}")
            log.debug(f"修复后的JSON: {fixed_json[:200]}...")
            raise
        except Exception as e:
            log.error(f"Java JSON修复过程失败: {e}")
            raise

    def _convert_java_user_to_fba(self, java_user: Dict) -> GetUserInfoWithRelationDetail:
        """
        将Java用户数据转换为FBA用户格式
        
        :param java_user: Java系统的用户数据
        :return: FBA格式的用户数据
        """
        try:
            # 提取Java用户信息
            user_info = java_user.get('user', {})

            # 处理时间字段
            from datetime import datetime

            def parse_java_time(time_value):
                """解析Java时间戳或时间字符串"""
                if isinstance(time_value, int):
                    # 毫秒时间戳
                    return datetime.fromtimestamp(time_value / 1000)
                elif isinstance(time_value, str):
                    # 时间字符串
                    try:
                        return datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')
                    except:
                        return datetime.now()
                else:
                    return datetime.now()

            # 构建FBA用户数据
            fba_user_data = {
                'id': user_info.get('userId', 1),
                'uuid': f"java-{user_info.get('userId', 1)}",
                'username': user_info.get('userName', 'admin'),
                'nickname': user_info.get('nickName', '管理员'),
                'email': user_info.get('email', '') or None,  # 空字符串转为None
                'phone': user_info.get('phonenumber', '') or None,  # 空字符串转为None
                'avatar': user_info.get('avatar') or None,  # 空字符串转为None，避免URL验证错误
                'status': 1 if user_info.get('status', '0') == '0' else 0,  # Java: 0=正常, 1=停用
                'is_superuser': user_info.get('admin', False),
                'is_staff': True,
                'is_multi_login': True,  # Java系统默认支持多端登录
                'join_time': parse_java_time(user_info.get('createTime', '2021-01-01 00:00:00')),
                'last_login_time': parse_java_time(java_user.get('loginTime')) if java_user.get('loginTime') else None,

                # 部门信息
                'dept_id': user_info.get('deptId'),
                'dept': {
                    'id': user_info.get('deptId'),
                    'name': user_info.get('dept', {}).get('deptName', ''),
                    'parent_id': user_info.get('dept', {}).get('parentId'),
                    'sort': 0,
                    'leader': user_info.get('dept', {}).get('leader'),
                    'phone': None,
                    'email': None,
                    'status': 1,
                    'del_flag': False,
                    'created_time': parse_java_time(user_info.get('dept', {}).get('createTime', '2021-01-01 00:00:00')),
                    'updated_time': None,
                } if user_info.get('dept') else None,

                # 角色信息
                'roles': [
                    {
                        'id': role.get('roleId'),
                        'name': role.get('roleName', ''),
                        'status': 1 if role.get('status', '0') == '0' else 0,
                        'is_filter_scopes': True,
                        'remark': role.get('remark'),
                        'created_time': parse_java_time(role.get('createTime', '2021-01-01 00:00:00')),
                        'updated_time': None,
                        'menus': [],  # 暂时为空，如需要可以扩展
                        'scopes': []  # 暂时为空，如需要可以扩展
                    }
                    for role in user_info.get('roles', [])
                ],

                # 权限信息
                'permissions': java_user.get('permissions', []),
            }
            
            return GetUserInfoWithRelationDetail(**fba_user_data)
            
        except Exception as e:
            log.error(f"转换Java用户数据失败: {e}")
            raise errors.TokenError(msg='用户数据转换失败')


# 全局Java适配器实例
java_adapter = JavaAdapter()
