// src/api/gateway/variable.ts
import request from '@/utils/request'

const getBaseUrl = () => {
  const host = window.location.hostname
  const port = '5000'
  return `http://${host}:${port}/openApi`
}

// 数据类型枚举
export enum DataTypeEnum {
  Object = 0,    // 对象
  String = 1,    // 字符串
  Boolean = 2,   // 布尔值
  Byte = 3,      // 字节
  Int16 = 4,     // 16位整数
  UInt16 = 5,    // 16位无符号整数
  Int32 = 6,     // 32位整数
  UInt32 = 7,    // 32位无符号整数
  Int64 = 8,     // 64位整数
  UInt64 = 9,    // 64位无符号整数
  Single = 10,   // 单精度浮点数
  Double = 11    // 双精度浮点数
}

// 保护类型枚举
export enum ProtectTypeEnum {
  ReadOnly = 0,   // 只读
  ReadWrite = 1,  // 读写
  WriteOnly = 2   // 只写
}

// 操作类型枚举
export enum ItemChangedType {
  Add = 0,        // 新增
  Update = 1      // 更新
}

// 获取变量列表请求参数
export interface VariablePageInput {
  pageIndex: number      // 页码
  pageSize: number       // 每页大小
  name?: string          // 变量名称（可选）
  deviceName?: string    // 设备名称（可选）
  registerAddress?: string // 变量地址（可选）
  businessDeviceId?: number // 业务设备ID（可选）
}

// 变量运行时数据 - 对应后端VariableRuntime
export interface VariableRuntime {
  id: number                    // 变量ID
  name: string                  // 变量名称
  deviceId: number              // 设备ID
  deviceName: string            // 设备名称
  description?: string          // 描述
  unit?: string                 // 单位
  intervalTime?: string         // 间隔时间
  registerAddress?: string      // 变量地址
  arrayLength?: number          // 数组长度
  otherMethod?: string          // 其他方法
  enable: boolean               // 是否启用
  protectType: number           // 保护类型（0:只读, 1:读写, 2:只写）
  dataType: number              // 数据类型
  readExpressions?: string      // 读取表达式
  writeExpressions?: string     // 写入表达式
  rpcWriteEnable: boolean       // 是否允许RPC写入
  value: any                    // 当前值
  isOnline: boolean             // 是否在线
  changeTime: string            // 最后变化时间
  collectTime: string           // 最后采集时间
  createTime: string            // 创建时间
  updateTime: string            // 更新时间
  
  // 报警相关字段
  alarmEnable: boolean          // 报警使能
  alarmDelay: number            // 报警延时
  hAlarmEnable: boolean         // 高报警使能
  hAlarmCode: number            // 高报警值
  hhAlarmEnable: boolean        // 高高报警使能
  hhAlarmCode: number           // 高高报警值
  lAlarmEnable: boolean         // 低报警使能
  lAlarmCode: number            // 低报警值
  llAlarmEnable: boolean        // 低低报警使能
  llAlarmCode: number           // 低低报警值
}

// 变量输入数据 - 对应后端VariableInput
export interface VariableInput {
  id?: number                    // 变量ID（更新时必填）
  deviceId: number               // 设备ID（必填）
  name: string                   // 变量名称（必填）
  description?: string           // 描述
  unit?: string                  // 单位
  intervalTime?: string          // 间隔时间
  registerAddress?: string       // 变量地址
  arrayLength?: number           // 数组长度
  otherMethod?: string           // 其他方法
  enable: boolean                // 是否启用
  protectType: number            // 保护类型（0:只读, 1:读写, 2:只写）
  dataType: number               // 数据类型
  readExpressions?: string       // 读取表达式
  writeExpressions?: string      // 写入表达式
  rpcWriteEnable: boolean        // 是否允许RPC写入
  initValue?: any                // 初始值
  saveValue: boolean             // 是否保存初始值
  
  // 报警配置
  alarmDelay: number             // 报警延时
  boolOpenAlarmEnable: boolean   // 布尔开报警使能
  boolCloseAlarmEnable: boolean  // 布尔关报警使能
  hAlarmEnable: boolean          // 高报警使能
  hAlarmCode?: number            // 高报警值
  hhAlarmEnable: boolean         // 高高报警使能
  hhAlarmCode?: number           // 高高报警值
  lAlarmEnable: boolean          // 低报警使能
  lAlarmCode?: number            // 低报警值
  llAlarmEnable: boolean         // 低低报警使能
  llAlarmCode?: number           // 低低报警值
  customAlarmEnable: boolean     // 自定义报警使能
  customAlarmCode?: string       // 自定义报警条件
  
  // 报警文本
  hAlarmText?: string            // 高报警文本
  hhAlarmText?: string           // 高高报警文本
  lAlarmText?: string            // 低报警文本
  llAlarmText?: string           // 低低报警文本
  customAlarmText?: string       // 自定义报警文本
  
  // 报警约束表达式
  hRestrainExpressions?: string  // 高报警约束
  hhRestrainExpressions?: string // 高高报警约束
  lRestrainExpressions?: string  // 低报警约束
  llRestrainExpressions?: string // 低低报警约束
  customRestrainExpressions?: string // 自定义报警约束
  
  // 备用字段
  remark1?: string               // 备用字段1
  remark2?: string               // 备用字段2
  remark3?: string               // 备用字段3
  remark4?: string               // 备用字段4
  remark5?: string               // 备用字段5
}

// 设备分页输入参数
export interface DevicePageInput {
  pageIndex: number      // 页码
  pageSize: number       // 每页大小
  name?: string          // 设备名称（可选）
  channelName?: string   // 通道名称（可选）
  pluginName?: string    // 插件名称（可选）
  pluginType?: number    // 插件类型（可选）
}

// 写入变量值请求格式
export interface WriteVariablesRequest {
  [deviceName: string]: {
    [variableName: string]: any
  }
}

// 写入变量值响应格式
export interface WriteVariablesResponse {
  [deviceName: string]: {
    [variableName: string]: {
      success: boolean
      message: string
    }
  }
}

// 获取变量列表
export function getVariableList(params: VariablePageInput) {
  const query = new URLSearchParams()
  query.append('pageIndex', String(params.pageIndex))
  query.append('pageSize', String(params.pageSize))
  if (params.name) query.append('name', params.name)
  if (params.deviceName) query.append('deviceName', params.deviceName)
  if (params.registerAddress) query.append('registerAddress', params.registerAddress)
  if (params.businessDeviceId) query.append('businessDeviceId', String(params.businessDeviceId))

  const url = `${getBaseUrl()}/runtimeInfo/variableList?${query.toString()}`
  return request({ url, method: 'get' })
}

// 批量获取变量实时值（高性能接口）
export function getVariableRealTimeValues(variableIds?: number[]) {
  const query = new URLSearchParams()
  if (variableIds && variableIds.length > 0) {
    // 直接传递ID列表，不进行URL编码
    query.append('variableIds', variableIds.join(','))
  }

  const url = `${getBaseUrl()}/runtimeInfo/variableRealTimeValues?${query.toString()}`
  return request({ url, method: 'get' })
}

// 获取变量统计信息
export function getVariableStatistics() {
  const url = `${getBaseUrl()}/runtimeInfo/variableStatistics`
  return request({ url, method: 'get' })
}

// 获取变量变化历史
export function getVariableChangeHistory(variableId: number, limit: number = 10) {
  const query = new URLSearchParams()
  query.append('variableId', String(variableId))
  query.append('limit', String(limit))

  const url = `${getBaseUrl()}/runtimeInfo/variableChangeHistory?${query.toString()}`
  return request({ url, method: 'get' })
}

// 批量保存变量（新增/更新）
export function batchSaveVariable(variables: VariableInput[], type: ItemChangedType, restart = true) {
  const url = `${getBaseUrl()}/control/batchSaveVariable`
  const query = new URLSearchParams()
  query.append('type', String(type))
  query.append('restart', String(restart))
  
  return request({
    url: `${url}?${query.toString()}`,
    method: 'post',
    data: variables
  })
}

// 新增变量
export function createVariable(data: VariableInput) {
  return batchSaveVariable([data], ItemChangedType.Add, true)
}

// 更新变量
export function updateVariable(data: VariableInput) {
  return batchSaveVariable([data], ItemChangedType.Update, true)
}

// 删除变量
export function deleteVariable(ids: number[], restart = true) {
  const url = `${getBaseUrl()}/control/deleteVariable`
  const query = new URLSearchParams()
  query.append('restart', String(restart))
  
  return request({
    url: `${url}?${query.toString()}`,
    method: 'post',
    data: ids
  })
}

// 写入变量值
export function writeVariables(data: WriteVariablesRequest) {
  const url = `${getBaseUrl()}/control/writeVariables`
  return request({
    url,
    method: 'post',
    data
  })
}

// 获取设备列表（用于变量关联）
export function getDeviceList(params: DevicePageInput) {
  const query = new URLSearchParams()
  query.append('pageIndex', String(params.pageIndex))
  query.append('pageSize', String(params.pageSize))
  if (params.name) query.append('name', params.name)
  if (params.channelName) query.append('channelName', params.channelName)
  if (params.pluginName) query.append('pluginName', params.pluginName)
  if (params.pluginType !== undefined) query.append('pluginType', String(params.pluginType))

  const url = `${getBaseUrl()}/runtimeInfo/deviceList?${query.toString()}`
  return request({ url, method: 'get' })
}


// 获取设备列表（用于变量关联）- 兼容旧接口
export function getDeviceListForVariable() {
  return getDeviceList({
    pageIndex: 1,
    pageSize: 1000
  })
}
