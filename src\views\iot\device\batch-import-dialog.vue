<template>
    <!-- 批量导入设备 -->
    <div>
        <el-dialog style="position: absolute; top: 100px;" :title="upload.title" v-model="upload.importDeviceDialog"
            width="550px" append-to-body>
            <div>
                <!-- <el-divider style="margin-top: -30px"></el-divider> -->
                <el-form label-position="top" :model="importForm" ref="importFormRef" :rules="importRules">
                    <el-form-item label="所属产品" prop="productName">
                        <el-input readonly v-model="importForm.productName" placeholder="请选择产品">
                            <template #append>
                                <div style="cursor:pointer" @click="selectProduct()">选择</div>
                            </template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label="上传文件" prop="fileList">
                        <el-upload style="width: 100%;" ref="uploadRef" :limit="1" accept=".xlsx, .xls"
                            v-model="importForm.fileList" :headers="upload.headers"
                            :action="`${upload.url}?productId=${importForm.productId}`" :disabled="upload.isUploading"
                            :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false"
                            :on-change="handleChange" :on-remove="handleRemove" drag>
                            <i><el-icon :size="60" color="#c0c4cc"><ele-UploadFilled /></el-icon></i>
                            <div style="font-size: 13px;" class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item>
                        <div style="text-align: center;" slot="tip">
                            <span class="el-upload__tip">仅允许导入xls、xlsx格式文件。</span>
                            <el-link type="primary" underline="never" style="font-size:12px;vertical-align: baseline;"
                                @click="importTemplate">下载模板</el-link>
                        </div>
                    </el-form-item>
                    <!-- <el-form-item>
                        <el-link type="primary" underline="never" style="font-size: 14px; vertical-align: baseline"
                            @click="importTemplate">
                            <el-icon>
                                <Download />
                            </el-icon>
                            下载设备导入模板
                        </el-link>
                    </el-form-item> -->
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="upload.importDeviceDialog = false">取消</el-button>
                    <el-button type="primary" @click="submitFileForm(importFormRef)">确定</el-button>
                </div>
            </template>
        </el-dialog>

        <!--添加从机对话框-->
        <el-dialog :title="productData.tableData.dialog.title" v-model="productData.tableData.dialog.isShowDialog"
            width="1000px" style="position: absolute; top: 100px;" append-to-body>
            <el-form ref="tempRef" :inline="true">
                <el-form-item label="产品名称" size="default">
                    <el-input v-model="productData.tableData.param.productName" placeholder="模板名称">
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="default"
                        @click="getProductList"><el-icon><ele-Search /></el-icon>搜索</el-button>
                    <el-button size="default" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                </el-form-item>
            </el-form>
            <el-table v-loading="productData.tableData.loading" :data="productData.tableData.data" highlight-current-row
                ref="multipleTable" style="width: 100%" border size="small" @row-click="rowClick"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="选择" width="50" align="center">
                    <template #default="scope">
                        <input type="radio" :checked="scope.row.isSelect" name="product" />
                    </template>
                </el-table-column>
                <el-table-column label="产品名称" align="center" prop="productName" />
                <el-table-column label="分类名称" align="center" prop="categoryName" />
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="授权码" align="center" prop="status" width="70">
                    <template #default="scope">
                        <el-tag type="success" v-if="scope.row.isAuthorize == 1">启用</el-tag>
                        <el-tag type="info" v-if="scope.row.isAuthorize == 0">未启用</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="产品状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="status" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="设备类型" align="center" prop="deviceType">
                    <template #default="scope">
                        <dict-tag :options="typelist" :value="scope.row.deviceType" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination v-show="productData.tableData.total > 0" :total="productData.tableData.total" class="mt15"
                style="justify-content: flex-end;" size="small" layout="total, prev, pager, next"
                v-model:current-page="productData.tableData.param.pageNum"
                v-model:page-size="productData.tableData.param.pageSize" @size-change="ontempHandleSizeChange"
                background @current-change="ontempHandleCurrentChange" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitSelect">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import { listProduct } from '/@/api/iot/product';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { Session } from '/@/utils/storage';
import { download } from '/@/utils/request';
import { ElMessageBox, FormInstance } from 'element-plus';
import { parseTime } from '/@/utils/next'
const dictStore = useDictStore();  // 使用 Pinia store


const emit = defineEmits(['save']);
const importFormRef = ref<FormInstance | null>(null);
// Define state and reactive variables
const importForm = ref<{
    productId: string | any;
    fileList: any[];
    productName: string;
}>({
    productId: null,
    fileList: [],
    productName: '',
});

const upload = reactive({
    importDeviceDialog: false,
    title: '批量导入',
    isUploading: false,
    headers: { Authorization: 'Bearer ' + Session.get('token') },
    url: `${import.meta.env.VITE_API_URL}iot/iotDevice/importData`,
});
const uploadRef = ref<any>(null);
const importRules = {
    productName: [{ required: true, message: '产品不能为空', trigger: 'blur' }],
    fileList: [{ required: true, message: '请上传文件', trigger: 'change' }],
};
const productData = reactive({
    tableData: {
        data: [] as any[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: 2,
            productName: ''
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
})
let product = reactive({
    productId: '',
    productName: '',
    deviceType: '',
    tenantId: '',
    tenantName: '',
    transport: '',
})
interface productOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const loading = ref(false)
const typelist = ref<productOption[]>([]);
const status = ref<productOption[]>([]);
const importTemplate = () => {
    download('/iot/iotDevice/uploadTemplate?type=' + 1, {}, `device_template_${new Date().getTime()}.xlsx`);
};

const handleChange = (file: any, fileList: any[]) => {
    importForm.value.fileList = fileList;
    if (importForm.value.fileList) {
        (importForm as any)?.value?.clearValidate?.('fileList');
    }
};

const handleRemove = (file: any, fileList: any[]) => {
    importForm.value.fileList = fileList;
    (importForm as any).value?.validateField?.('fileList');
};

const handleFileUploadProgress = (event: any, file: any, fileList: any[]) => {
    upload.isUploading = true;
};

const handleFileSuccess = (response: any, file: any, fileList: any[]) => {
    upload.importDeviceDialog = false;
    upload.isUploading = false;
    uploadRef.value.clearFiles();
    ElMessageBox.alert(`<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>${response.msg}</div>`, '导入结果', {
        dangerouslyUseHTMLString: true,
    });
    emit('save');
};
// 选择设备
const selectProduct = () => {
    productData.tableData.dialog.isShowDialog = true;
    productData.tableData.dialog.title = "选择模板";
    getProductList();
    getdictdata()
}
/** 查询设备采集变量模板列表(弹框列表) */
const getProductList = async () => {
    try {
        productData.tableData.loading = true;
        const response = await listProduct(productData.tableData.param)
        //产品列表初始化isSelect值，用于单选
        for (let i = 0; i < response.data.rows.length; i++) {
            response.data.rows[i].isSelect = false;
        }
        productData.tableData.data = response.data.rows;
        console.log('产品列表', productData.tableData.data);
        productData.tableData.total = response.data.total;
        if (importForm.value.productId != 0) {
            setRadioSelected(importForm.value.productId);
        }
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            productData.tableData.loading = false;
            // loading.value = false
        }, 500);
    }

}
/** 重置按钮操作 */
const resetQuery = () => {
    productData.tableData.param.pageNum = 1
    productData.tableData.param.pageSize = 10
    productData.tableData.param.status = 2
    productData.tableData.param.productName = ''
    getProductList()
}
/** 单选数据 */
const rowClick = (productdata: any) => {
    if (product != null) {
        setRadioSelected(productdata.productId);
        product = productdata as any;
    }
}
// 分页改变
const ontempHandleSizeChange = (val: number) => {
    productData.tableData.param.pageSize = val;
    getProductList();
};
// 分页改变
const ontempHandleCurrentChange = (val: number) => {
    productData.tableData.param.pageNum = val;
    getProductList();
};
// 取消按钮
const cancel = () => {
    productData.tableData.dialog.isShowDialog = false
}
/** 设置单选按钮选中 */
const setRadioSelected = (productId: any) => {
    for (let i = 0; i < productData.tableData.data.length; i++) {
        if (productData.tableData.data[i].productId == productId) {
            productData.tableData.data[i].isSelect = true;
        } else {
            productData.tableData.data[i].isSelect = false;
        }
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        typelist.value = await dictStore.fetchDict('iot_device_type')
        status.value = await dictStore.fetchDict('iot_product_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/*确认选择模板*/
const submitSelect = () => {
    productData.tableData.dialog.isShowDialog = false
    importForm.value.productId = product.productId;
    importForm.value.productName = product.productName;

}


const submitFileForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    const valid = await formEl.validate()
    if (valid) {
        uploadRef.value.submit();
        upload.importDeviceDialog = false;
    }

};
// 暴露变量
defineExpose({
    upload,
    importForm
});
</script>
