# 前端开发提示词指南

> 基于 vue-next-admin + Element Plus 的企业级后台管理系统开发规范

## 📋 目录

1. [设计原则与标准](#设计原则与标准)
2. [组件样式规范](#组件样式规范)
3. [布局和响应式设计](#布局和响应式设计)
4. [色彩和字体规范](#色彩和字体规范)
5. [代码实现模板](#代码实现模板)
6. [开发指导原则](#开发指导原则)

---

## 🎯 设计原则与标准

### 核心设计理念

基于知识库管理页面的成功实践，确立以下设计原则：

#### ✅ **简洁实用优先**
- 避免过度装饰和炫彩效果
- 专注于功能性和可读性
- 使用简洁的单色背景替代复杂渐变
- 保持视觉层次清晰

#### ✅ **功能性优先**
- 确保所有交互元素有足够的点击/触摸区域
- 优化按钮尺寸和间距，减少误操作
- 保留必要的视觉反馈，移除装饰性动画

#### ✅ **一致性原则**
- 统一的色彩体系和间距规范
- 标准化的组件配置和样式
- 保持与 Element Plus 设计语言的协调

---

## 🧩 组件样式规范

### 表格组件 (el-table)

#### 标准配置
```vue
<el-table
  v-loading="loading"
  :data="tableData"
  border
  size="small"
  :header-cell-style="headerCellStyle"
  style="width: 100%"
  :scroll-x="true"
  table-layout="auto"
>
```

#### 表头样式标准
```javascript
const headerCellStyle = {
  background: '#f5f7fa',
  color: '#606266',
  fontWeight: '600',
  fontSize: '14px',
  textAlign: 'center',
  borderBottom: '1px solid #e4e7ed',
  padding: '12px 8px'
};
```

#### 表格行配置
```css
:deep(.el-table) {
  .el-table__row {
    height: 50px; /* 为操作按钮预留空间 */
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  td {
    padding: 6px 4px;
    text-align: center;
  }
  
  .cell {
    padding: 0 6px;
    line-height: 18px;
    font-size: 13px;
  }
}
```

#### 列配置标准
- **选择列**: `width="55"` + `fixed="left"`
- **数据列**: 使用 `min-width` + `resizable`
- **操作列**: `width="200"` + `fixed="right"` + `align="right"`

### 按钮组件规范

#### 操作按钮标准尺寸
```css
.operation-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 0 8px;
  
  .el-button {
    padding: 8px 12px;
    font-size: 12px;
    height: 32px;
    min-width: 48px;
    border-radius: 4px;
    font-weight: 500;
  }
}
```

#### 按钮状态配置
- **主要按钮**: `type="primary"`
- **危险操作**: `type="danger"`
- **次要操作**: `type="default"` 或不设置

### 卡片组件规范

#### 标准卡片结构
```vue
<el-card shadow="hover">
  <template #header>
    <div class="card-header">
      <div>
        <h2 style="margin: 0; display: flex; align-items: center; gap: 8px;">
          <el-icon><Collection /></el-icon>
          页面标题
        </h2>
        <p style="margin: 8px 0 0 0; color: var(--el-text-color-regular); font-size: 14px;">
          页面描述信息
        </p>
      </div>
      <el-space>
        <!-- 操作按钮 -->
      </el-space>
    </div>
  </template>
  <!-- 卡片内容 -->
</el-card>
```

#### 卡片头部样式
```css
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}
```

---

## 📱 布局和响应式设计

### 页面布局标准

#### 基础布局结构
```vue
<div class="layout-padding">
  <el-card shadow="hover">
    <!-- 页面头部 -->
    <template #header>...</template>
    
    <!-- 统计卡片 (可选) -->
    <el-row :gutter="20" style="margin-bottom: 20px;">...</el-row>
    
    <!-- 搜索和操作栏 -->
    <el-card style="margin-bottom: 20px;">...</el-card>
    
    <!-- 主要内容区域 -->
    <div class="main-content">...</div>
    
    <!-- 分页 (如需要) -->
    <el-pagination />
  </el-card>
</div>
```

### 响应式断点标准

#### 桌面端 (>1024px)
- 表格行高: 50px
- 按钮高度: 32px
- 按钮间距: 8px
- 操作列宽度: 200px

#### 平板端 (768px - 1024px)
```css
@media (max-width: 1024px) {
  .operation-buttons {
    gap: 6px;
    
    .el-button {
      padding: 6px 10px;
      height: 28px;
      min-width: 44px;
    }
  }
}
```

#### 移动端 (≤768px)
```css
@media (max-width: 768px) {
  .operation-buttons {
    flex-direction: column;
    gap: 6px;
    
    .el-button {
      width: 100%;
      padding: 10px 8px;
      height: 36px;
      
      &:active {
        transform: scale(0.98);
      }
    }
  }
  
  :deep(.el-table) {
    .el-table__row {
      height: auto;
      min-height: 120px;
    }
  }
}
```

#### 小屏设备 (≤480px)
```css
@media (max-width: 480px) {
  .operation-buttons {
    .el-button {
      padding: 12px 8px;
      height: 40px;
      font-size: 13px;
    }
  }
}
```

### 固定列配置

#### 左侧固定列
```vue
<el-table-column type="selection" width="55" align="center" fixed="left" />
```

#### 右侧固定列
```vue
<el-table-column 
  label="操作" 
  align="right" 
  class-name="operation-column" 
  fixed="right"
  width="200"
/>
```

#### 固定列阴影效果
```css
:deep(.el-table) {
  .el-table-fixed-column--left {
    box-shadow: 1px 0 4px rgba(0, 0, 0, 0.1);
  }
  
  .el-table-fixed-column--right {
    box-shadow: -1px 0 4px rgba(0, 0, 0, 0.1);
  }
}
```

---

## 🎨 色彩和字体规范

### 标准色彩方案

#### 主色调
- **表头背景**: `#f5f7fa`
- **表头文字**: `#606266`
- **悬停背景**: `#f5f7fa`

#### Element Plus 变量使用
```css
/* 推荐使用 Element Plus CSS 变量 */
color: var(--el-text-color-primary);    /* 主要文字 */
color: var(--el-text-color-regular);    /* 常规文字 */
color: var(--el-text-color-secondary);  /* 次要文字 */
color: var(--el-text-color-placeholder); /* 占位文字 */
```

#### 状态色彩
- **成功**: Element Plus 默认 `success` 类型
- **警告**: Element Plus 默认 `warning` 类型  
- **危险**: Element Plus 默认 `danger` 类型
- **信息**: Element Plus 默认 `info` 类型

### 字体规范

#### 字体大小标准
```css
/* 页面标题 */
h2 { font-size: 18px; font-weight: 600; }

/* 表头文字 */
.table-header { font-size: 14px; font-weight: 600; }

/* 表格内容 */
.table-cell { font-size: 13px; font-weight: 400; }

/* 按钮文字 */
.button-text { font-size: 12px; font-weight: 500; }

/* 描述文字 */
.description { font-size: 12px; font-weight: 400; }
```

#### 行高标准
- **标题行高**: 1.4
- **正文行高**: 1.5  
- **表格行高**: 1.2
- **按钮行高**: 1.0

### 边框和圆角

#### 边框规范
```css
/* 标准边框 */
border: 1px solid #e4e7ed;

/* 表格边框 */
border-bottom: 1px solid #f0f0f0;
```

#### 圆角规范
```css
/* 按钮圆角 */
border-radius: 4px;

/* 卡片圆角 */
border-radius: 8px;
```

#### 阴影效果
```css
/* 轻微阴影 - 用于固定列 */
box-shadow: 1px 0 4px rgba(0, 0, 0, 0.1);

/* 卡片阴影 - Element Plus 默认 */
box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
```

---

## 💻 代码实现模板

### 完整页面模板

#### Vue 组件结构
```vue
<template>
  <div class="layout-padding">
    <el-card shadow="hover">
      <!-- 页面头部 -->
      <template #header>
        <div class="card-header">
          <div>
            <h2 style="margin: 0; display: flex; align-items: center; gap: 8px;">
              <el-icon><Collection /></el-icon>
              页面标题
            </h2>
            <p style="margin: 8px 0 0 0; color: var(--el-text-color-regular); font-size: 14px;">
              页面描述信息
            </p>
          </div>
          <el-space>
            <el-button type="primary" @click="handleCreate" :icon="Plus">
              创建
            </el-button>
          </el-space>
        </div>
      </template>

      <!-- 搜索和操作栏 -->
      <el-card style="margin-bottom: 20px;">
        <el-row :gutter="20" justify="space-between" align="middle">
          <el-col :xs="24" :sm="16" :md="16" :lg="16">
            <el-space wrap>
              <el-input
                v-model="searchKeyword"
                placeholder="搜索关键词"
                :prefix-icon="Search"
                clearable
                @input="handleSearch"
                style="width: 280px"
              />
            </el-space>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :lg="8">
            <el-space style="width: 100%; justify-content: flex-end;">
              <el-button @click="loadData" :icon="Refresh">刷新</el-button>
            </el-space>
          </el-col>
        </el-row>
      </el-card>

      <!-- 数据表格 -->
      <div class="data-table">
        <el-table
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          border
          size="small"
          :header-cell-style="headerCellStyle"
          style="width: 100%"
          :scroll-x="true"
          table-layout="auto"
        >
          <el-table-column type="selection" width="55" align="center" fixed="left" />

          <!-- 数据列示例 -->
          <el-table-column prop="name" label="名称" min-width="200" align="center" resizable>
            <template #default="{ row }">
              <div class="name-cell">
                <el-avatar :size="32" class="item-avatar">
                  <el-icon><Document /></el-icon>
                </el-avatar>
                <div class="item-info">
                  <div class="item-name">{{ row.name }}</div>
                  <div class="item-description">{{ row.description || '暂无描述' }}</div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" min-width="80" align="center" resizable>
            <template #default="{ row }">
              <el-tag :type="row.status === '1' ? 'success' : 'danger'" size="small">
                {{ row.status === '1' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="create_date" label="创建时间" min-width="140" align="center" resizable>
            <template #default="{ row }">
              {{ formatDate(row.create_date) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column
            label="操作"
            align="right"
            class-name="operation-column"
            fixed="right"
            width="200"
          >
            <template #default="{ row }">
              <div class="operation-buttons">
                <el-button
                  size="small"
                  type="primary"
                  @click="handleView(row)"
                >查看</el-button>
                <el-button
                  size="small"
                  type="primary"
                  @click="handleEdit(row)"
                >编辑</el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="handleDelete(row)"
                >删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-show="total > 0"
        size="small"
        :total="total"
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 30, 50]"
        :pager-count="5"
        background
        class="mt15"
        style="justify-content: flex-end;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts" name="PageName">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus,
  Search,
  Refresh,
  Delete,
  Document
} from '@element-plus/icons-vue';

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const total = ref(0);
const searchKeyword = ref('');

// 表头样式配置
const headerCellStyle = {
  background: '#f5f7fa',
  color: '#606266',
  fontWeight: '600',
  fontSize: '14px',
  textAlign: 'center',
  borderBottom: '1px solid #e4e7ed',
  padding: '12px 8px'
};

// 查询参数
const queryParams = reactive({
  page: 1,
  page_size: 20
});

// 生命周期
onMounted(() => {
  loadData();
});

// 方法定义
const loadData = async () => {
  // 实现数据加载逻辑
};

const handleSearch = () => {
  queryParams.page = 1;
  loadData();
};

const handleCreate = () => {
  // 实现创建逻辑
};

const handleView = (row: any) => {
  // 实现查看逻辑
};

const handleEdit = (row: any) => {
  // 实现编辑逻辑
};

const handleDelete = async (row: any) => {
  // 实现删除逻辑
};

const handleSelectionChange = (selection: any[]) => {
  // 实现选择变更逻辑
};

const handleSizeChange = (size: number) => {
  queryParams.page_size = size;
  queryParams.page = 1;
  loadData();
};

const handleCurrentChange = (page: number) => {
  queryParams.page = page;
  loadData();
};

const formatDate = (dateStr: string | undefined): string => {
  if (!dateStr) return '-';
  try {
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN');
  } catch {
    return dateStr;
  }
};
</script>
```

### 标准样式模板

#### 基础样式结构
```vue
<style scoped>
/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

/* 名称单元格样式 */
.name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  padding: 6px 4px;
}

.item-info {
  text-align: center;
  min-width: 0;
}

.item-name {
  font-weight: 500;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.item-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表格样式 */
:deep(.el-table) {
  .el-table__row {
    height: 50px;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  td {
    padding: 6px 4px;
    text-align: center;
  }

  .cell {
    padding: 0 6px;
    line-height: 18px;
    font-size: 13px;
  }

  th {
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    padding: 12px 8px;

    .cell {
      color: #606266;
      font-weight: 600;
    }
  }

  .el-tag {
    font-size: 12px;
    height: 20px;
    line-height: 18px;
  }

  .el-table-fixed-column--left {
    box-shadow: 1px 0 4px rgba(0, 0, 0, 0.1);
  }

  .el-table-fixed-column--right {
    box-shadow: -1px 0 4px rgba(0, 0, 0, 0.1);
  }
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 0 8px;

  .el-button {
    padding: 8px 12px;
    font-size: 12px;
    height: 32px;
    min-width: 48px;
    border-radius: 4px;
    font-weight: 500;
  }
}

:deep(.operation-column) {
  .cell {
    padding: 0 6px;
  }
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .operation-buttons {
    gap: 6px;

    .el-button {
      padding: 6px 10px;
      height: 28px;
      min-width: 44px;
    }
  }
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 15px;
  }

  .name-cell {
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 4px 2px;
  }

  .operation-buttons {
    flex-direction: column;
    gap: 6px;

    .el-button {
      width: 100%;
      padding: 10px 8px;
      height: 36px;

      &:active {
        transform: scale(0.98);
      }
    }
  }

  :deep(.el-table) {
    .el-table__row {
      height: auto;
      min-height: 120px;
    }
  }
}

@media (max-width: 480px) {
  .operation-buttons {
    .el-button {
      padding: 12px 8px;
      height: 40px;
      font-size: 13px;
    }
  }
}
</style>
```

---

## 🎯 开发指导原则

### AI 开发者指导

当您作为 AI 开发助手为该项目开发新页面或优化现有页面时，请严格遵循以下原则：

#### ✅ **应该做的事情**

##### 1. **设计风格**
- ✅ 使用简洁的单色背景 (`#f5f7fa`) 作为表头
- ✅ 保持与 Element Plus 设计语言的一致性
- ✅ 优先考虑功能性和可用性
- ✅ 使用标准的 Element Plus 组件和配置
- ✅ 遵循既定的色彩和字体规范

##### 2. **布局结构**
- ✅ 使用标准的页面布局模板
- ✅ 保持卡片头部的统一结构
- ✅ 实现完整的响应式设计
- ✅ 为操作按钮预留足够的空间
- ✅ 使用固定列提升用户体验

##### 3. **交互体验**
- ✅ 确保按钮有足够的点击区域 (最小 32px 高度)
- ✅ 提供适当的按钮间距 (8px)
- ✅ 实现移动端友好的触摸体验
- ✅ 添加必要的加载状态和反馈
- ✅ 保持操作的一致性

##### 4. **代码质量**
- ✅ 使用 TypeScript 进行类型定义
- ✅ 遵循 Vue 3 Composition API 最佳实践
- ✅ 保持代码结构清晰和可维护
- ✅ 使用语义化的 CSS 类名
- ✅ 添加必要的注释说明

#### ❌ **不应该做的事情**

##### 1. **设计禁忌**
- ❌ 不要使用复杂的渐变背景
- ❌ 不要添加过度的装饰性动画
- ❌ 不要使用过于炫彩的色彩方案
- ❌ 不要破坏 Element Plus 的设计一致性
- ❌ 不要忽视可访问性要求

##### 2. **布局错误**
- ❌ 不要使用固定的像素宽度 (除操作列外)
- ❌ 不要忽视移动端适配
- ❌ 不要让按钮过小或间距不足
- ❌ 不要破坏表格的响应式特性
- ❌ 不要忽视不同屏幕尺寸的测试

##### 3. **交互问题**
- ❌ 不要让操作按钮难以点击
- ❌ 不要忽视触摸设备的体验
- ❌ 不要缺少必要的用户反馈
- ❌ 不要让界面响应缓慢
- ❌ 不要忽视错误处理

##### 4. **代码问题**
- ❌ 不要使用内联样式 (除必要情况)
- ❌ 不要忽视代码复用性
- ❌ 不要写过于复杂的嵌套结构
- ❌ 不要忽视性能优化
- ❌ 不要缺少错误边界处理

### 开发检查清单

在完成页面开发后，请使用以下检查清单确保质量：

#### 🎨 **视觉检查**
- [ ] 表头使用标准的 `#f5f7fa` 背景色
- [ ] 按钮尺寸符合标准 (桌面端 32px 高度)
- [ ] 间距符合规范 (按钮间距 8px)
- [ ] 色彩使用 Element Plus 变量
- [ ] 字体大小和权重符合标准

#### 📱 **响应式检查**
- [ ] 桌面端 (>1024px) 显示正常
- [ ] 平板端 (768px-1024px) 适配良好
- [ ] 移动端 (≤768px) 按钮垂直排列
- [ ] 小屏设备 (≤480px) 触摸友好
- [ ] 表格在各尺寸下可正常滚动

#### 🔧 **功能检查**
- [ ] 所有按钮可正常点击
- [ ] 表格排序和筛选功能正常
- [ ] 分页功能工作正常
- [ ] 搜索功能响应及时
- [ ] 加载状态显示正确

#### 💻 **代码检查**
- [ ] 使用 TypeScript 类型定义
- [ ] 遵循 Vue 3 Composition API
- [ ] CSS 类名语义化
- [ ] 代码结构清晰
- [ ] 包含必要的错误处理

### 常见问题解决方案

#### Q: 表格在移动端显示不佳？
**A**: 确保使用了完整的响应式样式，特别是操作按钮的垂直排列和表格行高的自适应。

#### Q: 按钮点击区域太小？
**A**: 检查按钮的 `height` 和 `min-width` 设置，确保符合标准尺寸要求。

#### Q: 表头样式与设计不符？
**A**: 使用标准的 `headerCellStyle` 配置，避免自定义复杂的样式。

#### Q: 页面在不同设备上表现不一致？
**A**: 检查响应式断点设置，确保覆盖了所有主要的屏幕尺寸。

### 版本更新指南

当需要更新设计规范时：

1. **向后兼容**: 确保新规范不破坏现有页面
2. **渐进升级**: 优先更新核心页面，再推广到其他页面
3. **文档同步**: 及时更新本指南文档
4. **团队沟通**: 确保所有开发者了解变更内容

---

## 📚 参考资源

- [Element Plus 官方文档](https://element-plus.org/)
- [Vue 3 官方文档](https://vuejs.org/)
- [vue-next-admin 框架文档](https://gitee.com/lyt-top/vue-next-admin)
- [知识库管理页面实现](src/views/ai/kb/kbm/index.vue)

---

**最后更新**: 2024年12月
**版本**: v1.0
**维护者**: AI 开发团队
