<template>
    <div style="padding-left: 20px">
        <el-row :gutter="10" class="mb8" :justify="'space-between'">
            <div>
                <el-button v-if="productInfo.status == 1" plain v-auths="['iot:firmware:add']" size="default"
                    type="primary" class="ml5" @click="handleAdd('add')">
                    <el-icon><ele-Plus /></el-icon>
                    新增
                </el-button>
                <el-button v-auths="['iot:firmware:list']" plain size="default" type="warning" class="ml10"
                    @click="getTableData">
                    <el-icon><ele-Refresh /></el-icon>
                    刷新
                </el-button>
            </div>
        </el-row>
        <el-table v-loading="state.tableData.loading" :data="state.tableData.firmwareList" border style="width: 100%"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" size="small">
            <el-table-column label="固件名称" align="center" prop="firmwareName" />
            <el-table-column label="固件版本" align="center" prop="version" width="120">
                <template #default="scope">
                    <span>Version </span> {{ scope.row.version }}
                </template>
            </el-table-column>
            <el-table-column label="状态" align="center" prop="isLatest" width="80">
                <template #default="scope">
                    <el-tag type="success" v-if="scope.row.isLatest == 1">最新</el-tag>
                    <el-tag type="info" v-else>默认</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="下载地址" align="center" prop="filePath" min-width="200">
                <template #default="scope">
                    <el-link :href="getDownloadUrl(scope.row.filePath)" underline="never" type="success">{{
                        getDownloadUrl(scope.row.filePath) }}</el-link>
                </template>
            </el-table-column>
            <el-table-column label="固件描述" align="center" prop="remark" min-width="200" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button size="small" @click="handleUpdate('edit', scope.row)" type="primary"
                        v-auths="['iot:firmware:query']"
                        v-if="productInfo.status != 2"><el-icon><ele-View /></el-icon>查看</el-button>
                    <el-button size="small" @click="handleDelete(scope.row)" type="danger"
                        v-auths="['iot:firmware:remove']"
                        v-if="productInfo.status != 2"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!-- 添加或修改产品固件对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogState.dialog.title"
            v-model="dialogState.dialog.isShowDialog" width="500px" append-to-body>
            <el-form ref="DialogFormRef" :rules="rules" :model="dialogState.ruleForm" label-width="80px">
                <el-form-item label="固件名称" prop="firmwareName">
                    <el-input v-model="dialogState.ruleForm.firmwareName" placeholder="请输入固件名称" />
                </el-form-item>
                <el-form-item label="固件版本" prop="version">
                    <el-input v-model="dialogState.ruleForm.version" placeholder="请输入固件版本" type="number" step="0.1" />
                </el-form-item>
                <el-form-item label="最新固件" prop="isLatest">
                    <el-switch v-model="dialogState.ruleForm.isLatest" active-text="" inactive-text="" :active-value="1"
                        :inactive-value="0">
                    </el-switch>
                    <el-link type="info" underline="never"
                        style="font-size:12px;margin-left:15px;">提示：产品中只能有一个最新固件</el-link>
                </el-form-item>
                <el-form-item label="固件上传" prop="filePath">
                    <fileUpload ref="file-upload" v-model="dialogState.ruleForm.filePath" :limit="1" :fileSize="10"
                        :fileType='["bin", "zip", "pdf"]'></fileUpload>
                </el-form-item>
                <el-form-item label="固件描述" prop="remark">
                    <el-input v-model="dialogState.ruleForm.remark" type="textarea" :rows="4" placeholder="请输入固件信息" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel">取 消</el-button>
                    <el-button type="primary" @click="submitForm(DialogFormRef)" v-auths="['iot:firmware:edit']">{{ dialogState.dialog.submitTxt }}
                        </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>


<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { addFirmware, delFirmware, getFirmware, listFirmware, updateFirmware } from '/@/api/iot/firmware';
import { Session } from '/@/utils/storage';
import fileUpload from '/@/components/FileUpload/index.vue'
import { parseTime } from '/@/utils/next'

// 定义变量内容
const DialogFormRef = ref();
// 定义 props
const props = defineProps({
    modelValue: {
        type: Object
    }
});
// interface modeData {
//     identifier: string;
//     modelName: string;
//     type: number;
//     isChart: boolean;
//     isMonitor: boolean;
//     isHistory: boolean;
//     isSharePerm: boolean;
//     isReadonly: boolean;
//     specs: string;

// }
const state = reactive({
    tableData: {
        firmwareList: [],
        total: 0,
        loading: false,
        queryParams: {
            pageNum: 1,
            pageSize: 100,
            firmwareName: null,
            productName: null,
            productId: 0,
            isSys: null,
        },
    },
});
const initialState = reactive({
    ruleForm: {
        firmwareName: null,
        version: 1.0,
        isLatest: 0,
        filePath: '',
        remark: null,
        firmwareId: '',
        productName: null,
        productId: 0,
    },
    dialog: {
        isShowDialog: false,
        title: '',
        submitTxt: '',
    },
})
// 初始化 state
const dialogState = reactive({
    ruleForm: { ...initialState.ruleForm },
    // deptData: [...initialState.deptData],
    dialog: { ...initialState.dialog },
});
// 校验规则
const rules = reactive({
    firmwareName: [{
        required: true,
        message: "固件名称不能为空",
        trigger: "blur"
    }],
    version: [{
        required: true,
        message: "固件版本不能为空",
        trigger: "blur"
    }],
    filePath: [{
        required: true,
        message: "文件路径不能为空",
        trigger: "blur"
    }],

})
// 上传参数
const upload = reactive({
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: {
        Authorization: "Bearer " + Session.get('token')
    },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + "/iot/tool/upload",
    // 上传的文件列表
    fileList: [] as any
})
// 获取文件路径
// const getFilePath = (data: any) => {
//     dialogState.ruleForm.filePath = data;
// }
//接收父组件得产品信息
const productInfo = ref({
    status: '' as any,
    productId: '' as any,
    productName: '' as any,
    isModbus: false,
})
const ids = ref() //firmwareId
// 监听props的变化
watch(() => props.modelValue, (newVal) => {
    try {
        productInfo.value = newVal as any
        if (productInfo.value && productInfo.value.productId != 0) {
            state.tableData.queryParams.productId = productInfo.value.productId;
            dialogState.ruleForm.productId = productInfo.value.productId;
            dialogState.ruleForm.productName = productInfo.value.productName;
            getTableData();
        }

    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
const getDownloadUrl = (path: any) => {
    return window.location.origin + import.meta.env.VITE_APP_BASE_API + path;
}
/** 查询产品物模型列表 */
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        
        const response = await listFirmware(state.tableData.queryParams);
        state.tableData.total = response.data.total;
        state.tableData.firmwareList = response.data.rows;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
// 清空弹框
const resetState = () => {
    dialogState.ruleForm = { ...initialState.ruleForm }
    dialogState.dialog = { ...initialState.dialog }
    dialogState.ruleForm.productId = productInfo.value.productId;
    dialogState.ruleForm.productName = productInfo.value.productName;
};
/** 新增按钮操作 */
const handleAdd = (type: any) => {
    resetState();
    dialogState.dialog.isShowDialog = true;
    dialogState.dialog.title = "添加产品固件";
    dialogState.dialog.submitTxt = '新 增'
    upload.fileList = [];
}
/** 修改按钮操作 */
const handleUpdate = (type: string, row: any | undefined) => {
    resetState();
    dialogState.dialog.submitTxt = '修 改'
    const firmwareId = row.firmwareId || ids.value
    getFirmware(firmwareId).then(response => {
        dialogState.ruleForm = response.data.data;
        dialogState.dialog.isShowDialog = true;
        dialogState.dialog.title = "修改产品固件";
        upload.fileList = [{
            name: dialogState.ruleForm.firmwareName,
            url: dialogState.ruleForm.filePath
        }];
    });
}
// 提交
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (dialogState.ruleForm.firmwareId != '') {
                updateFirmware(dialogState.ruleForm).then(response => {
                    //  刷新页面
                    getTableData();
                    dialogState.dialog.isShowDialog = false;
                    ElMessage.success('修改成功');
                });
            } else {
                addFirmware(dialogState.ruleForm).then(response => {
                    //  刷新页面
                    getTableData();
                    dialogState.dialog.isShowDialog = false;
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 取消按钮
const cancel = () => {
    dialogState.dialog.isShowDialog = false;
    resetState();
}
/** 删除按钮操作 */
const handleDelete = (row: { firmwareId: any; }) => {
    const firmwareIds = row.firmwareId;
    ElMessageBox.confirm('是否确认删除产品固件编号为"' + firmwareIds + '"的数据项？', '系统提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delFirmware(firmwareIds).then(res => {
                ElMessage.success('删除成功');
                getTableData()
            });

        })
        .catch(() => { });

}

</script>
<style>
.specsColor {
    background-color: #fcfcfc;
}
</style>