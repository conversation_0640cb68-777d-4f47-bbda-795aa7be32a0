2025-08-25 07:57:20.626 | ERROR    | 38c64d53b4144ccd9e44ad637b55063a | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
              │   └ <function verify at 0x00000262155E3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xfa\xe4\xa3Y\x18\xf14\xa3Pi\xd4J\x07\xd4\x96\xe9/a\x85\x027K\x03\xc8\xb8\x84\xcf\x9aQ\x01;\xa0\xc8$\x93\xed\x16\xeb\xfd'.\...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9'
    └ <function _verify_signature at 0x00000262156CF740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x00000262155E3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                    └ <function jwt_decode at 0x0000026215745F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002620E8AA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000026210DC8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000026210DCBA60>
    └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026210DCBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
           │       └ <function run at 0x000002621060F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026218DCA340>
           │      └ <function Runner.run at 0x0000026210AAB2E0>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026210AA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026210B78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026210AAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026210604860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000262193658A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026219365D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000026218EA7410>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x00000262193658A0>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026219365D00>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000026213DF5260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026219365D00>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x0000026219392BD0>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000026214DFB7E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000026216AFE5A0>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 └ <function jwt_authentication at 0x0000026215746480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000026215745C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000026216725B50>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-25 07:57:22.107 | ERROR    | d28dcea8805a4b538463f2113e6502f5 | JWT 授权异常：cannot access local variable 'json' where it is not associated with a value
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 159, in decode
    payload = jws.verify(token, key, algorithms, verify=verify_signature)
              │   │      │      │    │                  └ True
              │   │      │      │    └ ['HS256']
              │   │      │      └ 'abcdefghijklfastbeesmartrstuvwxyz'
              │   │      └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
              │   └ <function verify at 0x00000262155E3C40>
              └ <module 'jose.jws' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jws.py'>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 77, in verify
    _verify_signature(signing_input, header, signature, key, algorithms)
    │                 │              │       │          │    └ ['HS256']
    │                 │              │       │          └ 'abcdefghijklfastbeesmartrstuvwxyz'
    │                 │              │       └ b"\xfa\xe4\xa3Y\x18\xf14\xa3Pi\xd4J\x07\xd4\x96\xe9/a\x85\x027K\x03\xc8\xb8\x84\xcf\x9aQ\x01;\xa0\xc8$\x93\xed\x16\xeb\xfd'.\...
    │                 │              └ {'alg': 'HS512'}
    │                 └ b'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9'
    └ <function _verify_signature at 0x00000262156CF740>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jws.py", line 259, in _verify_signature
    raise JWSError("The specified alg value is not allowed")
          └ <class 'jose.exceptions.JWSError'>

jose.exceptions.JWSError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 96, in jwt_decode
    payload = jwt.decode(
              │   └ <function decode at 0x00000262155E3A60>
              └ <module 'jose.jwt' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\jose\\jwt.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\jose\jwt.py", line 161, in decode
    raise JWTError(e)
          └ <class 'jose.exceptions.JWTError'>

jose.exceptions.JWTError: The specified alg value is not allowed


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 286, in jwt_authentication
    token_payload = jwt_decode(token)
                    │          └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                    └ <function jwt_decode at 0x0000026215745F80>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 110, in jwt_decode
    raise errors.TokenError(msg='Token 无效')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 89, in authenticate_java_token
    raise errors.TokenError(msg='Token 无效或已过期')
          │      └ <class 'backend.common.exception.errors.TokenError'>
          └ <module 'backend.common.exception.errors' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\backend\\common\\excepti...

backend.common.exception.errors.TokenError: 401: Token 无效或已过期


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000002620E8AA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000026210DC8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000026210DCBA60>
    └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026210DCBB00>
           │       │   └ <uvicorn.server.Server object at 0x0000026218DC0FB0>
           │       └ <function run at 0x000002621060F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026218DCA340>
           │      └ <function Runner.run at 0x0000026210AAB2E0>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026210AA8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026218BA3FB0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026210B78D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026210AAAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026210604860>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finished result=None>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finished result=None>)>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\base.py", line 144, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026219367920>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000262193679C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
          └ <backend.middleware.access_middleware.AccessMiddleware object at 0x0000026218EA7410>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer ey...
          │    │               │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026219367920>
          │    │               │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000262193679C0>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x0000026213DF5260>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540...
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x00000262193679C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x0000026218E22540>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\starlette\middleware\authentication.py", line 36, in __call__
    auth_result = await self.backend.authenticate(conn)
                        │    │       │            └ <starlette.requests.HTTPConnection object at 0x00000262193019D0>
                        │    │       └ <function JwtAuthMiddleware.authenticate at 0x0000026214DFB7E0>
                        │    └ <backend.middleware.jwt_auth_middleware.JwtAuthMiddleware object at 0x0000026216AFE5A0>
                        └ <starlette.middleware.authentication.AuthenticationMiddleware object at 0x0000026218DE21E0>

> File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\middleware\jwt_auth_middleware.py", line 74, in authenticate
    user = await jwt_authentication(token)
                 │                  └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 └ <function jwt_authentication at 0x0000026215746480>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\jwt.py", line 315, in jwt_authentication
    return await java_adapter.authenticate_java_token(token)
                 │            │                       └ 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6Ijg4Yjg4M2M1LWY5MTktNGQ5Zi05NjRkLTc3NTkwYjA1N2E0MyJ9.-uSjWRjxNKNQadRKB9SW6S9hhQ...
                 │            └ <function JavaAdapter.authenticate_java_token at 0x0000026215745C60>
                 └ <backend.common.security.java_adapter.JavaAdapter object at 0x0000026216725B50>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\common\security\java_adapter.py", line 108, in authenticate_java_token
    except json.JSONDecodeError as e:
           │    └ <class 'json.decoder.JSONDecodeError'>
           └ <module 'json' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py'>

UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
2025-08-25 08:06:11.655 | ERROR    | 8c857efa480148068bb98e4de341d2d6 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:11.655 | ERROR    | 8c857efa480148068bb98e4de341d2d6 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:16.254 | ERROR    | 11aa3b126373401d9e55903aa0332da6 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:16.254 | ERROR    | 11aa3b126373401d9e55903aa0332da6 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:17.921 | ERROR    | 069e7893bbf14eb5a3e100305d8aff2e | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:17.922 | ERROR    | 069e7893bbf14eb5a3e100305d8aff2e | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:20.809 | ERROR    | 4472bb9b37594457af277f2dd89cb662 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:20.809 | ERROR    | 4472bb9b37594457af277f2dd89cb662 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:27.015 | ERROR    | 5ef6abd64e0f417faef4be72464365f5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:06:27.016 | ERROR    | 5ef6abd64e0f417faef4be72464365f5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:23.186 | ERROR    | f38b54c8628e4f9f8574c6023a6d92ec | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:23.186 | ERROR    | f38b54c8628e4f9f8574c6023a6d92ec | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:27.671 | ERROR    | a9d72d745f014795af08157a45bdf5e5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:13:27.671 | ERROR    | a9d72d745f014795af08157a45bdf5e5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:19:19.886 | ERROR    | bf8b0e19cfe6424086b5b25a7f62ea38 | RAGFlow业务错误: code=100, message=<NotFound '404: Not Found'>
2025-08-25 08:19:19.887 | ERROR    | bf8b0e19cfe6424086b5b25a7f62ea38 | 开始文档解析失败: <NotFound '404: Not Found'>
2025-08-25 08:19:30.663 | ERROR    | 24c1809510374767b7e66151fde14b9b | RAGFlow业务错误: code=100, message=<NotFound '404: Not Found'>
2025-08-25 08:19:30.663 | ERROR    | 24c1809510374767b7e66151fde14b9b | 开始文档解析失败: <NotFound '404: Not Found'>
2025-08-25 08:20:57.982 | ERROR    | bfa88eb42618494eb0ac87a5e030e847 | RAGFlow业务错误: code=401, message=<Unauthorized '401: Unauthorized'>
2025-08-25 08:20:57.982 | ERROR    | bfa88eb42618494eb0ac87a5e030e847 | 开始文档解析失败: <Unauthorized '401: Unauthorized'>
2025-08-25 08:22:15.640 | ERROR    | 0fc922b8c1044f12ae1c7fbc60cb55df | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:22:15.640 | ERROR    | 0fc922b8c1044f12ae1c7fbc60cb55df | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:22:15.650 | ERROR    | 0fc922b8c1044f12ae1c7fbc60cb55df | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:22:15.650 | ERROR    | 0fc922b8c1044f12ae1c7fbc60cb55df | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:27.471 | ERROR    | 2bfa3babe693498985676dc26f2cdded | RAGFlow业务错误: code=100, message=TypeError("object of type 'NoneType' has no len()")
2025-08-25 08:24:27.505 | ERROR    | 2bfa3babe693498985676dc26f2cdded | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:27.506 | ERROR    | 2bfa3babe693498985676dc26f2cdded | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:27.519 | ERROR    | 2bfa3babe693498985676dc26f2cdded | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:27.519 | ERROR    | 2bfa3babe693498985676dc26f2cdded | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:30.000 | ERROR    | 9b62bf7e3b604e5a9caca024bde52319 | RAGFlow业务错误: code=100, message=TypeError("object of type 'NoneType' has no len()")
2025-08-25 08:24:30.030 | ERROR    | 9b62bf7e3b604e5a9caca024bde52319 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:30.030 | ERROR    | 9b62bf7e3b604e5a9caca024bde52319 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:30.046 | ERROR    | 9b62bf7e3b604e5a9caca024bde52319 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:24:30.047 | ERROR    | 9b62bf7e3b604e5a9caca024bde52319 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:31.632 | ERROR    | b90dbffae0b74f3fba49e2ceb68da2e3 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:31.632 | ERROR    | b90dbffae0b74f3fba49e2ceb68da2e3 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:31.642 | ERROR    | b90dbffae0b74f3fba49e2ceb68da2e3 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:31.643 | ERROR    | b90dbffae0b74f3fba49e2ceb68da2e3 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:39.186 | ERROR    | 5a91f0c1bae948a98e69ce44964558d0 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:39.186 | ERROR    | 5a91f0c1bae948a98e69ce44964558d0 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:39.199 | ERROR    | 5a91f0c1bae948a98e69ce44964558d0 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:28:39.199 | ERROR    | 5a91f0c1bae948a98e69ce44964558d0 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:30.423 | ERROR    | fb577c9d6d604d659fe533b6266d912a | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:30.423 | ERROR    | fb577c9d6d604d659fe533b6266d912a | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:30.433 | ERROR    | fb577c9d6d604d659fe533b6266d912a | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:30.434 | ERROR    | fb577c9d6d604d659fe533b6266d912a | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:32.413 | ERROR    | 5bd4e69df7c2474c894539d05c2c6edc | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:32.413 | ERROR    | 5bd4e69df7c2474c894539d05c2c6edc | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:32.427 | ERROR    | 5bd4e69df7c2474c894539d05c2c6edc | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:32.427 | ERROR    | 5bd4e69df7c2474c894539d05c2c6edc | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:33.579 | ERROR    | aeba2a8337594ba5915dbd84f6983b0d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:33.579 | ERROR    | aeba2a8337594ba5915dbd84f6983b0d | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:33.593 | ERROR    | aeba2a8337594ba5915dbd84f6983b0d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:33.593 | ERROR    | aeba2a8337594ba5915dbd84f6983b0d | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:36.931 | ERROR    | d9dc11b16c8647d692efb910832e3896 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:36.932 | ERROR    | d9dc11b16c8647d692efb910832e3896 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:36.945 | ERROR    | d9dc11b16c8647d692efb910832e3896 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:36.946 | ERROR    | d9dc11b16c8647d692efb910832e3896 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:37.973 | ERROR    | 482e27921d6947c1b4a7117a634671e5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:37.973 | ERROR    | 482e27921d6947c1b4a7117a634671e5 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:37.986 | ERROR    | 482e27921d6947c1b4a7117a634671e5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:37.987 | ERROR    | 482e27921d6947c1b4a7117a634671e5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:39.212 | ERROR    | f8a9706e190641738f1a549d9a6b7522 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:39.212 | ERROR    | f8a9706e190641738f1a549d9a6b7522 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:39.225 | ERROR    | f8a9706e190641738f1a549d9a6b7522 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:39.227 | ERROR    | f8a9706e190641738f1a549d9a6b7522 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:40.914 | ERROR    | 71e84a035b90453ba3c48f2c82a6bb6f | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:40.914 | ERROR    | 71e84a035b90453ba3c48f2c82a6bb6f | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:40.928 | ERROR    | 71e84a035b90453ba3c48f2c82a6bb6f | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:32:40.928 | ERROR    | 71e84a035b90453ba3c48f2c82a6bb6f | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:09.416 | ERROR    | 6fc25142db9247aca778d011bead2b9f | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:09.416 | ERROR    | 6fc25142db9247aca778d011bead2b9f | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:09.426 | ERROR    | 6fc25142db9247aca778d011bead2b9f | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:09.426 | ERROR    | 6fc25142db9247aca778d011bead2b9f | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:11.772 | ERROR    | f33fe0f2f67545b595b00299525e51a5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:11.772 | ERROR    | f33fe0f2f67545b595b00299525e51a5 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:11.784 | ERROR    | f33fe0f2f67545b595b00299525e51a5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:33:11.785 | ERROR    | f33fe0f2f67545b595b00299525e51a5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:04.033 | ERROR    | d48a567092124c8fb8b5401e69f2abcb | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:04.033 | ERROR    | d48a567092124c8fb8b5401e69f2abcb | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:04.042 | ERROR    | d48a567092124c8fb8b5401e69f2abcb | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:04.043 | ERROR    | d48a567092124c8fb8b5401e69f2abcb | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:41.128 | ERROR    | 7c278f2fd4744141994ff7eac0ffb966 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:41.128 | ERROR    | 7c278f2fd4744141994ff7eac0ffb966 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:41.138 | ERROR    | 7c278f2fd4744141994ff7eac0ffb966 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:41.139 | ERROR    | 7c278f2fd4744141994ff7eac0ffb966 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:42.737 | ERROR    | d4159e44e4794273b5b9f9ad98f24c0d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:42.737 | ERROR    | d4159e44e4794273b5b9f9ad98f24c0d | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:42.747 | ERROR    | d4159e44e4794273b5b9f9ad98f24c0d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:42.748 | ERROR    | d4159e44e4794273b5b9f9ad98f24c0d | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.269 | ERROR    | c708984df7094d24b031fb15f0a50afa | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.269 | ERROR    | c708984df7094d24b031fb15f0a50afa | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.278 | ERROR    | c708984df7094d24b031fb15f0a50afa | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.278 | ERROR    | c708984df7094d24b031fb15f0a50afa | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.602 | ERROR    | 009b48a13dbe48bda3fb2bbe99f34578 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.602 | ERROR    | 009b48a13dbe48bda3fb2bbe99f34578 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.612 | ERROR    | 009b48a13dbe48bda3fb2bbe99f34578 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:43.612 | ERROR    | 009b48a13dbe48bda3fb2bbe99f34578 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:44.001 | ERROR    | 952b98389b2140bb8c8f6adae1a4f571 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:44.001 | ERROR    | 952b98389b2140bb8c8f6adae1a4f571 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:44.013 | ERROR    | 952b98389b2140bb8c8f6adae1a4f571 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:44.013 | ERROR    | 952b98389b2140bb8c8f6adae1a4f571 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:48.410 | ERROR    | 749525b028e0487f87df0284739e9f41 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:48.411 | ERROR    | 749525b028e0487f87df0284739e9f41 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:48.426 | ERROR    | 749525b028e0487f87df0284739e9f41 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:48.427 | ERROR    | 749525b028e0487f87df0284739e9f41 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:53.893 | ERROR    | 46d2711c2ad947d9b054f8ddf0b976d4 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:53.893 | ERROR    | 46d2711c2ad947d9b054f8ddf0b976d4 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:53.902 | ERROR    | 46d2711c2ad947d9b054f8ddf0b976d4 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:53.903 | ERROR    | 46d2711c2ad947d9b054f8ddf0b976d4 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:54.560 | ERROR    | 59fe1cc2aec944f188aabc6b9c454712 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:54.561 | ERROR    | 59fe1cc2aec944f188aabc6b9c454712 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:54.570 | ERROR    | 59fe1cc2aec944f188aabc6b9c454712 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:54.570 | ERROR    | 59fe1cc2aec944f188aabc6b9c454712 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.096 | ERROR    | e80c53a4e2e943a99570506b8e973fac | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.097 | ERROR    | e80c53a4e2e943a99570506b8e973fac | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.106 | ERROR    | e80c53a4e2e943a99570506b8e973fac | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.106 | ERROR    | e80c53a4e2e943a99570506b8e973fac | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.398 | ERROR    | 93334a829045486891a68f9d9a017856 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.398 | ERROR    | 93334a829045486891a68f9d9a017856 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.409 | ERROR    | 93334a829045486891a68f9d9a017856 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.409 | ERROR    | 93334a829045486891a68f9d9a017856 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.705 | ERROR    | 15d0936b09ef41a09b053abb2370a551 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.705 | ERROR    | 15d0936b09ef41a09b053abb2370a551 | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.716 | ERROR    | 15d0936b09ef41a09b053abb2370a551 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:34:55.716 | ERROR    | 15d0936b09ef41a09b053abb2370a551 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:38:31.770 | ERROR    | b79989d8d49146f1a2be577fb07d551d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:38:31.771 | ERROR    | b79989d8d49146f1a2be577fb07d551d | 文档解析失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:38:31.789 | ERROR    | b79989d8d49146f1a2be577fb07d551d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 08:38:31.789 | ERROR    | b79989d8d49146f1a2be577fb07d551d | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 08:40:16.701 | ERROR    | cc24cf51f14e412483bbca7ed835f749 | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 08:40:16.702 | ERROR    | cc24cf51f14e412483bbca7ed835f749 | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:11:33.429 | ERROR    | 304a00cf49464a61ac5b26b9463ab3aa | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:11:33.430 | ERROR    | 304a00cf49464a61ac5b26b9463ab3aa | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:11:50.900 | ERROR    | 57517fe0f4c84136b920800085087746 | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:11:50.900 | ERROR    | 57517fe0f4c84136b920800085087746 | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:20:36.551 | ERROR    | 1b605b74ca8549ffb925013012283e3e | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:20:36.551 | ERROR    | 1b605b74ca8549ffb925013012283e3e | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:20:51.673 | ERROR    | dc8f992a911943f6865a256b3923f84e | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:20:51.673 | ERROR    | dc8f992a911943f6865a256b3923f84e | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:21:11.598 | ERROR    | 9c85d49ad1324066a7c79735d99a0589 | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:21:11.598 | ERROR    | 9c85d49ad1324066a7c79735d99a0589 | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:34:47.906 | ERROR    | fd3e95911ee04b008f651ed65862bacd | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:34:47.906 | ERROR    | fd3e95911ee04b008f651ed65862bacd | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 09:36:15.763 | ERROR    | a60d24896304498793f6a37cc677d24f | RAGFlow业务错误: code=500, message=test (1).pptx: No module named 'aspose'
2025-08-25 09:36:15.763 | ERROR    | a60d24896304498793f6a37cc677d24f | 上传文档失败: test (1).pptx: No module named 'aspose'
2025-08-25 10:19:43.995 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x000001A99DD3A8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x000001A9A02D8B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x000001A9A02DBA60>
    └ <uvicorn.server.Server object at 0x000001A9A8279D60>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x000001A9A02DBB00>
           │       │   └ <uvicorn.server.Server object at 0x000001A9A8279D60>
           │       └ <function run at 0x000001A99FB1F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001A9A82DA340>
           │      └ <function Runner.run at 0x000001A99FFBB2E0>
           └ <asyncio.runners.Runner object at 0x000001A9A8291820>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001A99FFB8EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001A9A8291820>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x000001A9A0088D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001A99FFBAC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001A99FB14860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=1836, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 56455)>
    └ <_ProactorSocketTransport closing fd=1836>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-25 10:38:34.126 | ERROR    | 53a2ab8996ba49cc96c24cf75cdac279 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:38:34.133 | ERROR    | 53a2ab8996ba49cc96c24cf75cdac279 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:01.293 | ERROR    | bca8191330ec484c8d874920f8d41234 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:01.293 | ERROR    | bca8191330ec484c8d874920f8d41234 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:04.562 | ERROR    | 21b4469abce04e9abc63495b20a71a22 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:04.562 | ERROR    | 21b4469abce04e9abc63495b20a71a22 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:05.485 | ERROR    | 37f3329e000c4792860057139bcb6ac5 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:05.485 | ERROR    | 37f3329e000c4792860057139bcb6ac5 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:05.738 | ERROR    | 9aeab21882cf4d6c92dbd98c9404262c | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:05.739 | ERROR    | 9aeab21882cf4d6c92dbd98c9404262c | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:05.883 | ERROR    | f8690db72fe44dd69f457c205e705ae6 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:05.885 | ERROR    | f8690db72fe44dd69f457c205e705ae6 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:09.138 | ERROR    | 52937affaf924807a98d193c28fef538 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:09.139 | ERROR    | 52937affaf924807a98d193c28fef538 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:12.385 | ERROR    | ee2dc1c9576045c386d20c59ae787b48 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:12.385 | ERROR    | ee2dc1c9576045c386d20c59ae787b48 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:13.014 | ERROR    | ae8b4eed59a3401b8ab56738bda4efb9 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:13.015 | ERROR    | ae8b4eed59a3401b8ab56738bda4efb9 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:13.172 | ERROR    | 06cc86b8a868415b96b2c051c81e6aca | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:13.173 | ERROR    | 06cc86b8a868415b96b2c051c81e6aca | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:13.314 | ERROR    | 81db210005b94c26a9f799a5fc626ac0 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:13.314 | ERROR    | 81db210005b94c26a9f799a5fc626ac0 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:15.328 | ERROR    | 5f6e117dd93843a583dce981b3b95aa2 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:15.329 | ERROR    | 5f6e117dd93843a583dce981b3b95aa2 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:17.029 | ERROR    | 24f9c9325ecb4052933621f7ce3d92ad | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:39:17.032 | ERROR    | 24f9c9325ecb4052933621f7ce3d92ad | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:48:04.058 | ERROR    | c5368d8b14144ffb895a42d6d6c89b2a | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:48:04.058 | ERROR    | c5368d8b14144ffb895a42d6d6c89b2a | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:49:49.502 | ERROR    | d35f278ccc394e83a23bff0fc168e1f6 | 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:49:49.502 | ERROR    | d35f278ccc394e83a23bff0fc168e1f6 | 删除文档分块失败: 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:49:49.526 | ERROR    | 088b41de967a427d8b9418d3cc7dfbe7 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:49:49.526 | ERROR    | 088b41de967a427d8b9418d3cc7dfbe7 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:50:17.180 | ERROR    | 39c3bffd437249bb86b188fd54e3fcae | 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:50:17.180 | ERROR    | 39c3bffd437249bb86b188fd54e3fcae | 删除文档分块失败: 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:50:17.209 | ERROR    | 68bd906645f3410399e9b1f8db62a2b9 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:50:17.209 | ERROR    | 68bd906645f3410399e9b1f8db62a2b9 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:52:08.264 | ERROR    | 2b6515ea830b4eac98afc214e8f39bf0 | 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:52:08.264 | ERROR    | 2b6515ea830b4eac98afc214e8f39bf0 | 删除文档分块失败: 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:52:08.297 | ERROR    | 3320b9240cde43fa94b37d07c9cc34db | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:52:08.297 | ERROR    | 3320b9240cde43fa94b37d07c9cc34db | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:53:19.642 | ERROR    | c81cfcf066a449769f48c4f6edfe66fe | 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:53:19.642 | ERROR    | c81cfcf066a449769f48c4f6edfe66fe | 删除文档分块失败: 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:53:19.663 | ERROR    | e8e7689a58834e96af9ae79752e0262c | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:53:19.663 | ERROR    | e8e7689a58834e96af9ae79752e0262c | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:54:12.539 | ERROR    | 3b9cab030afc449cb0f46ba770d18215 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:12.539 | ERROR    | 3b9cab030afc449cb0f46ba770d18215 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:13.644 | ERROR    | 401013949719498e8f477a9652f14016 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:13.644 | ERROR    | 401013949719498e8f477a9652f14016 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:36.128 | ERROR    | 12369055a7184217829fcbac5ae8a18e | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:36.128 | ERROR    | 12369055a7184217829fcbac5ae8a18e | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:36.560 | ERROR    | a6c4991770644f49a06e7ce329f26481 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:36.561 | ERROR    | a6c4991770644f49a06e7ce329f26481 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:39.048 | ERROR    | 8f70b68e1f85434b818ccd554d3ee825 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:39.048 | ERROR    | 8f70b68e1f85434b818ccd554d3ee825 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:39.468 | ERROR    | 2da40d73c45a4d528682de41d0f13109 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:39.468 | ERROR    | 2da40d73c45a4d528682de41d0f13109 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:39.610 | ERROR    | 8a60ba0c444c4976b40130598a2442ee | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:39.610 | ERROR    | 8a60ba0c444c4976b40130598a2442ee | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:52.102 | ERROR    | b8e2c876bbf24d088382a04e2fd9cc06 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:54:52.103 | ERROR    | b8e2c876bbf24d088382a04e2fd9cc06 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:55:03.670 | ERROR    | aded1aca7c13480f8a5ac4e3fcdca05e | 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:55:03.670 | ERROR    | aded1aca7c13480f8a5ac4e3fcdca05e | 删除文档分块失败: 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 10:55:03.700 | ERROR    | 8f183f4346cc4fc8bd3e56bf2e6f1659 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 10:55:03.701 | ERROR    | 8f183f4346cc4fc8bd3e56bf2e6f1659 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 10:55:20.809 | ERROR    | 1f5bc4749a95438c8c00be1b852344b6 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 10:55:20.809 | ERROR    | 1f5bc4749a95438c8c00be1b852344b6 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:00:47.793 | ERROR    | d64f19433ca842439314ee2f47f944b2 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:00:47.793 | ERROR    | d64f19433ca842439314ee2f47f944b2 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:09.792 | ERROR    | 1f151ca487f9422686061ed8108f2cf0 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:09.792 | ERROR    | 1f151ca487f9422686061ed8108f2cf0 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:10.643 | ERROR    | 33e8682d94f94cf3ad8ed29ff21a6dfb | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:10.644 | ERROR    | 33e8682d94f94cf3ad8ed29ff21a6dfb | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:10.809 | ERROR    | 426169d515ae40e284104c5ee16abb33 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:10.809 | ERROR    | 426169d515ae40e284104c5ee16abb33 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:11.002 | ERROR    | a2196d30b4d941a38fad003b2b422d1d | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:11.002 | ERROR    | a2196d30b4d941a38fad003b2b422d1d | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:02:18.999 | ERROR    | 575af4196d7846799c3f3cc34acdb9bf | 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 11:02:19.000 | ERROR    | 575af4196d7846799c3f3cc34acdb9bf | 删除文档分块失败: 删除文档分块失败: 'DocumentChunkDeleteRequest' object has no attribute 'get'
2025-08-25 11:02:19.034 | ERROR    | be9d2fdc3cb44c77ad164441156ebe36 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:02:19.034 | ERROR    | be9d2fdc3cb44c77ad164441156ebe36 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:03:06.121 | ERROR    | 6235720f67f94c11bcbdb9c485242c4c | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:03:06.122 | ERROR    | 6235720f67f94c11bcbdb9c485242c4c | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:03:09.484 | ERROR    | 5f1ae7d785324070bb8eac42bb7dd9d0 | 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:03:09.484 | ERROR    | 5f1ae7d785324070bb8eac42bb7dd9d0 | 创建文档分块失败: 创建文档分块失败: 'DocumentChunkCreateRequest' object has no attribute 'get'
2025-08-25 11:04:35.342 | ERROR    | fde664f4ee45401bbfdb4ecc30ffebe0 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:04:35.342 | ERROR    | fde664f4ee45401bbfdb4ecc30ffebe0 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:05:06.207 | ERROR    | 1f11b33d83504634bb9f7cfad74ba58e | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:05:06.207 | ERROR    | 1f11b33d83504634bb9f7cfad74ba58e | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:05:11.268 | ERROR    | 5d37c4d34cfc4c55a9ecc6e07ad057bd | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:05:11.268 | ERROR    | 5d37c4d34cfc4c55a9ecc6e07ad057bd | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:06:10.942 | ERROR    | 1f4d9fb73b68420f8fa954984965ef88 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:06:10.942 | ERROR    | 1f4d9fb73b68420f8fa954984965ef88 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:09:29.677 | ERROR    | 3a68b54bc4be488f99208b04792daa20 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:09:29.677 | ERROR    | 3a68b54bc4be488f99208b04792daa20 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:10:00.174 | ERROR    | cb4946fb78284ad6af95610d4fe138b7 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:10:00.175 | ERROR    | cb4946fb78284ad6af95610d4fe138b7 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:10:10.906 | ERROR    | 77481038d44f4206865308a02fab4123 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:10:10.906 | ERROR    | 77481038d44f4206865308a02fab4123 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:13:44.583 | ERROR    | 2294f7380d7f4d56a6e88991f3eff021 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:13:44.583 | ERROR    | 2294f7380d7f4d56a6e88991f3eff021 | 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:13:44.583 | ERROR    | 2294f7380d7f4d56a6e88991f3eff021 | 重新解析文档失败: 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:13:56.612 | ERROR    | d31a3c19e9a844c2a1f156d06048a132 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:13:56.612 | ERROR    | d31a3c19e9a844c2a1f156d06048a132 | 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:13:56.612 | ERROR    | d31a3c19e9a844c2a1f156d06048a132 | 重新解析文档失败: 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:04.910 | ERROR    | 231abdd24c5f45ed82c8d75a078228da | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:04.910 | ERROR    | 231abdd24c5f45ed82c8d75a078228da | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:06.378 | ERROR    | bff36b6740174a778f9ea11d4f178909 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:06.378 | ERROR    | bff36b6740174a778f9ea11d4f178909 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:07.218 | ERROR    | 2079f0024b95419abd81d172af87a0e4 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:07.219 | ERROR    | 2079f0024b95419abd81d172af87a0e4 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:07.695 | ERROR    | 7a03e26b5cca4f40afcba81ca11eb15d | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:07.695 | ERROR    | 7a03e26b5cca4f40afcba81ca11eb15d | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:07.952 | ERROR    | 2bc8d2dc04ff410fab8e797dee4838ad | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:07.952 | ERROR    | 2bc8d2dc04ff410fab8e797dee4838ad | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:08.102 | ERROR    | 884ae3bbc55b4f698828e2fc4b677996 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:08.102 | ERROR    | 884ae3bbc55b4f698828e2fc4b677996 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:13.632 | ERROR    | 1e7eaba10d8c4a6f86141c326dfebfa3 | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:14:13.632 | ERROR    | 1e7eaba10d8c4a6f86141c326dfebfa3 | 开始文档解析失败: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:16:51.606 | ERROR    | 20d2baae387444a2bc35d31036fea2ed | Java token认证失败: 401: Token 格式错误
2025-08-25 11:17:00.198 | ERROR    | f244eb486c0342d599fa08378775e1e8 | Java token认证失败: 401: Token 格式错误
2025-08-25 11:17:02.130 | ERROR    | 91c088e1f7a54057a5a6da1d91f5705c | Java token认证失败: 401: Token 格式错误
2025-08-25 11:17:02.875 | ERROR    | c47af43de1b34878aa38324a12616656 | Java token认证失败: 401: Token 格式错误
2025-08-25 11:18:06.836 | ERROR    | 05244b965d044943bb3344e0554896ba | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:18:06.836 | ERROR    | 05244b965d044943bb3344e0554896ba | 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:18:06.837 | ERROR    | 05244b965d044943bb3344e0554896ba | 重新解析文档失败: 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:18:45.241 | ERROR    | cb8afeced4f4483f98501684de92fbbd | RAGFlow业务错误: code=102, message=Can't stop parsing document with progress at 0 or 100
2025-08-25 11:18:45.242 | ERROR    | cb8afeced4f4483f98501684de92fbbd | 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 11:18:45.242 | ERROR    | cb8afeced4f4483f98501684de92fbbd | 重新解析文档失败: 重新解析文档失败: 400: Can't stop parsing document with progress at 0 or 100
2025-08-25 14:33:11.460 | ERROR    | - | Exception in callback _ProactorBasePipeTransport._call_connection_lost(None)
handle: <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
Traceback (most recent call last):

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 35, in <module>
    main()
    └ <function main at 0x0000022A13ECA8E0>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\backend\start_stable.py", line 19, in main
    uvicorn.run(
    │       └ <function run at 0x0000022A16438B80>
    └ <module 'uvicorn' from 'C:\\AI\\fastapi_best_arc\\fastapi_best_architecture\\.venv\\Lib\\site-packages\\uvicorn\\__init__.py'>

  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\main.py", line 580, in run
    server.run()
    │      └ <function Server.run at 0x0000022A1643BA60>
    └ <uvicorn.server.Server object at 0x0000022A1CE115E0>
  File "C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000022A1643BB00>
           │       │   └ <uvicorn.server.Server object at 0x0000022A1CE115E0>
           │       └ <function run at 0x0000022A15C7F060>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000022A1E3DB840>
           │      └ <function Runner.run at 0x0000022A1611B2E0>
           └ <asyncio.runners.Runner object at 0x0000022A1E30F5F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-11' coro=<Server.serve() running at C:\AI\fastapi_best_arc\fastapi_best_architecture\.venv\Lib\site-...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000022A16118EA0>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000022A1E30F5F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000022A161E8D60>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000022A1611AC00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000022A15C74860>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle _ProactorBasePipeTransport._call_connection_lost(None)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\proactor_events.py", line 165, in _call_connection_lost
    self._sock.shutdown(socket.SHUT_RDWR)
    │    │     │        │      └ 2
    │    │     │        └ <module 'socket' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py'>
    │    │     └ <method 'shutdown' of '_socket.socket' objects>
    │    └ <socket.socket fd=992, family=2, type=1, proto=0, laddr=('127.0.0.1', 8000), raddr=('127.0.0.1', 59328)>
    └ <_ProactorSocketTransport closing fd=992>

ConnectionResetError: [WinError 10054] 远程主机强迫关闭了一个现有的连接。
2025-08-25 14:33:14.841 | ERROR    | c041e5e89a64491db1ccdecf3cd6eff7 | Java token认证失败: 401: Token 无效或已过期
2025-08-25 14:33:24.614 | ERROR    | 85e9a3c2717b4c4da8072d695a9151a2 | Java token认证失败: 401: Token 无效或已过期
2025-08-25 14:33:24.616 | ERROR    | fa7cb197c9b64711a7392fb387a2c863 | Java token认证失败: 401: Token 无效或已过期
2025-08-25 14:33:37.040 | ERROR    | 79178ed3649942059234a32f1c25e6ac | Java token认证失败: 401: Token 无效或已过期
2025-08-25 14:33:37.043 | ERROR    | ed8ef6d420f74212ac05e3cd5ff86978 | Java token认证失败: 401: Token 无效或已过期
2025-08-25 14:33:37.046 | ERROR    | 18ebf88e7a254afd8cb272d6a9b58e5c | Java token认证失败: 401: Token 无效或已过期
2025-08-25 14:37:54.037 | ERROR    | 25957ca48edf428aae515e1f7b7fafee | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:38:15.074 | ERROR    | f4c2df1e9bf349e2ae17ef36343263b7 | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:38:36.118 | ERROR    | a73f30b2a49d4224924a77a9650431ce | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:38:57.149 | ERROR    | 053967de5c744b738a3dfc8ca91e3588 | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:39:18.200 | ERROR    | 52dd9ad1489e4ef5b3403bed98a64ed0 | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:39:39.232 | ERROR    | 11878d4f0e7d40fb889dabebe18821df | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:39:52.505 | ERROR    | c496638d36444a8e96a8a37bb176f78f | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:40:00.266 | ERROR    | cdba247259e64ec2b83ce6473383fadd | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:40:09.642 | ERROR    | 173f3a68f4f34db99781650fd2042389 | Java token认证失败: Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
2025-08-25 14:41:22.285 | ERROR    | - | ❌ 数据库 redis 连接异常 Error 22 connecting to 192.168.66.40:5862. 信号灯超时时间已到.
