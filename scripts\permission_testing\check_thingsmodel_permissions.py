#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查通用物模型权限标识
"""

import asyncio
import sys
sys.path.append('.')
from backend.database.java_db import get_java_db
from sqlalchemy import text

async def check_thingsmodel_permissions():
    async for session in get_java_db():
        print('=== 检查通用物模型权限标识 ===')
        
        # 查找所有与thingsModel相关的菜单权限
        result = await session.execute(text('''
            SELECT menu_id, menu_name, perms, menu_type, status
            FROM sys_menu 
            WHERE perms LIKE '%thingsModel%' OR perms LIKE '%template%'
            ORDER BY menu_id
        '''))
        menus = result.fetchall()
        
        print('数据库中的权限标识:')
        for menu in menus:
            print(f'  ID:{menu[0]} | {menu[1]} | 权限:{menu[2]} | 类型:{menu[3]} | 状态:{menu[4]}')
        
        # 检查pythontest用户拥有的相关权限
        result = await session.execute(text('''
            SELECT DISTINCT m.perms
            FROM sys_user u
            JOIN sys_user_role ur ON u.user_id = ur.user_id
            JOIN sys_role r ON ur.role_id = r.role_id
            JOIN sys_role_menu rm ON r.role_id = rm.role_id
            JOIN sys_menu m ON rm.menu_id = m.menu_id
            WHERE u.user_name = 'pythontest' 
            AND (m.perms LIKE '%thingsModel%' OR m.perms LIKE '%template%')
            ORDER BY m.perms
        '''))
        user_perms = result.fetchall()
        
        print('\npythontest用户拥有的相关权限:')
        for perm in user_perms:
            print(f'  ✅ {perm[0]}')
        
        print('\n=== 问题分析 ===')
        print('API需要权限: iot:thingsModel:list')
        has_required_perm = any('iot:thingsModel:list' == perm[0] for perm in user_perms)
        status = '✅ 有' if has_required_perm else '❌ 没有'
        print(f'用户是否有所需权限: {status}')
        
        if not has_required_perm:
            print('\n需要添加缺失的权限或修正现有权限标识')
            
            # 建议解决方案
            print('\n=== 解决方案建议 ===')
            print('方案1: 修改数据库中的权限标识')
            print('  将 iot:template:* 改为 iot:thingsModel:*')
            print('方案2: 修改Java代码中的权限要求')
            print('  将 @PreAuthorize("@ss.hasPermi(\'iot:thingsModel:list\')") 改为')
            print('  @PreAuthorize("@ss.hasPermi(\'iot:template:list\'")')

if __name__ == '__main__':
    asyncio.run(check_thingsmodel_permissions())
