#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Java /getInfo接口 - 验证权限数据传递
"""

import asyncio
import sys
import json
sys.path.append('.')

async def test_java_getinfo_api():
    print('=== 测试Java /getInfo接口权限数据 ===')
    
    import aiohttp
    
    # 使用pythontest用户的JWT token
    token = 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImJkYWNhNDY5LTFjMzAtNDk0Zi1iMDRlLThhOTc1NGRjM2MyNiJ9.leue4Ni75SoSBDCe6y2VO2XGKOEIvISYBSBvh6_yiQn6DnSnmoI_a3cabnj7A6iUXLGOpmLa0JtImdNCJxhIWw'
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # 调用本地前端代理的getInfo接口
            async with session.get(
                'http://localhost/prod-api/getInfo',
                headers=headers
            ) as response:
                print(f'响应状态码: {response.status}')
                
                if response.status == 200:
                    data = await response.json()
                    print(f'响应数据结构: {type(data)}')

                    # Java系统的响应格式直接在根级别，不在data字段中
                    if 'user' in data and 'permissions' in data:
                        info_data = data
                        
                        # 用户信息
                        user = info_data.get('user', {})
                        print(f'\n=== 用户信息 ===')
                        print(f'用户名: {user.get("userName", "unknown")}')
                        print(f'用户ID: {user.get("userId", "unknown")}')
                        print(f'昵称: {user.get("nickName", "unknown")}')
                        
                        # 角色信息
                        roles = info_data.get('roles', [])
                        print(f'\n=== 角色信息 ===')
                        print(f'角色数量: {len(roles)}')
                        for role in roles:
                            print(f'  - {role}')
                        
                        # 权限信息 - 这是关键部分
                        permissions = info_data.get('permissions', [])
                        print(f'\n=== 权限信息分析 ===')
                        print(f'权限总数: {len(permissions)}')
                        
                        # 检查关键的iot:template权限
                        template_permissions = [p for p in permissions if 'template' in p]
                        print(f'\n通用物模型相关权限 ({len(template_permissions)}个):')
                        for perm in template_permissions:
                            print(f'  ✅ {perm}')
                        
                        # 检查知识库权限
                        kb_permissions = [p for p in permissions if 'knowledge' in p]
                        print(f'\n知识库相关权限 ({len(kb_permissions)}个):')
                        for perm in kb_permissions:
                            print(f'  ✅ {perm}')
                        
                        # 检查是否有超级管理员权限
                        has_super_admin = '*:*:*' in permissions
                        print(f'\n超级管理员权限: {"✅ 有" if has_super_admin else "❌ 无"}')
                        
                        # 显示所有权限（前20个）
                        print(f'\n所有权限列表 (前20个):')
                        for i, perm in enumerate(permissions[:20], 1):
                            print(f'  {i:2d}. {perm}')
                        
                        if len(permissions) > 20:
                            print(f'  ... 还有 {len(permissions) - 20} 个权限')
                        
                        # 分析问题
                        print(f'\n=== 问题分析 ===')
                        if not template_permissions:
                            print('❌ 关键问题: /getInfo接口没有返回任何iot:template相关权限!')
                            print('   这解释了为什么前端显示"权限不足"')
                        else:
                            print('✅ /getInfo接口正确返回了iot:template权限')
                            print('   问题可能在前端权限验证逻辑')
                        
                    else:
                        print('响应中没有data字段')
                        print(f'完整响应: {data}')
                else:
                    error_text = await response.text()
                    print(f'请求失败: {error_text}')
                    
    except Exception as e:
        print(f'请求异常: {e}')

if __name__ == '__main__':
    asyncio.run(test_java_getinfo_api())
