<template>
    <div style="padding-left: 20px">
        <el-row :gutter="120">
            <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" style="margin-bottom: 50px">
                <div class="tabs">
                    <div class="tab" :class="active == 0 ? 'actives' : '' " @click="activeChange(0)">固有属性</div> 
                    <div class="tab" :class="active == 1 ? 'actives' : ''" @click="activeChange(1)">运维属性</div>
                    <div class="tab" :class="active == 2 ? 'actives' : ''" @click="activeChange(2)">遥测属性</div>
                    <div class="tab" :class="active == 3 ? 'actives' : ''" @click="activeChange(3)">通讯属性</div>
                    <div class="tab" :class="active == 4 ? 'actives' : ''" @click="activeChange(4)">虚拟属性</div>
<!--                    <div class="tab" :class="active == 5 ? 'actives' : ''" @click="activeChange(5)">功能</div>-->
                    <div class="tab" :class="active == 7 ? 'actives' : ''" @click="activeChange(7)">关系</div>
                </div>
                <div>
                    <!-- 遥测属性 设备监测图表-->
                    <div style="width: auto;max-width: 820px;display: inline-block;">
                        <div v-show="active == 2" style="background-color: #f5f7fa;display: flex;flex-wrap: wrap;
                             justify-content: space-between;padding: 10px; border-radius: 15px;"
                            v-if="deviceInfo.chartList.length > 0">
                            <div v-for="(item, index) in deviceInfo.chartList" :key="index" style="margin: 10px;">
                                <el-card value="hover" style="border-radius: 30px; width: 380px;">
                                    <div :ref="setChartRef" style="height: 230px; width: 185px; margin: 0 auto"></div>
                                </el-card>
                            </div>
                        </div>
                    </div>
                    <el-descriptions :column="1" border style="margin-bottom: 50px; width: 800px;"
                        :label-width="'200px'">
                        <!-- 设备物模型-->
                        <!-- 固有属性 -->
                        <div v-if="active == 0">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 1 && item.subType == 1">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 18px" title="指令发送" v-if="item.isReadonly == 1">
                                                </el-button>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="!valueUnEnable && item.isReadonly == 0">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                       物模型名称                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, index) in item.datatype.arrayModel"
                                                    :key="index" :label="item.name + (index + 1)">
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            placeholder="请输入字符串" size="default" v-model="model.value"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请输入小数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input type="integer" placeholder="请输入整数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0">
                                                                    </el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                        <!-- 运维属性 -->
                        <div v-if="active == 1">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <!-- <template #label>
                                            <span class="custom-tabs-label">
                                                <span slot="label"><span style="color:red;">* </span>基本信息</span>
                                            </span>
                                        </template> -->
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 1 && item.subType == 2">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 18px" title="指令发送" v-if="item.isReadonly == 1">
                                                </el-button>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="!valueUnEnable && item.isReadonly == 0">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, index) in item.datatype.arrayModel"
                                                    :key="index" :label="item.name + (index + 1)">
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            placeholder="请输入字符串" size="default" v-model="model.value"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请输入小数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input type="integer" placeholder="请输入整数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0">
                                                                    </el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                        <!-- 遥测属性 -->
                        <!-- 通讯属性 -->
                        <div v-if="active == 3">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <!-- <template #label>
                                            <span class="custom-tabs-label">
                                                <span slot="label"><span style="color:red;">* </span>基本信息</span>
                                            </span>
                                        </template> -->
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 1 && item.subType == 4">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 18px" title="指令发送" v-if="item.isReadonly == 1">
                                                </el-button>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="!valueUnEnable && item.isReadonly == 0">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, index) in item.datatype.arrayModel"
                                                    :key="index" :label="item.name + (index + 1)">
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            placeholder="请输入字符串" size="default" v-model="model.value"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请输入小数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input type="integer" placeholder="请输入整数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0">
                                                                    </el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                        <!-- 虚拟属性 -->
                        <div v-if="active == 4">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <!-- <template #label>
                                            <span class="custom-tabs-label">
                                                <span slot="label"><span style="color:red;">* </span>基本信息</span>
                                            </span>
                                        </template> -->
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 1 && item.subType == 5">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 18px" title="指令发送" v-if="item.isReadonly == 1">
                                                </el-button>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="!valueUnEnable && item.isReadonly == 0">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, index) in item.datatype.arrayModel"
                                                    :key="index" :label="item.name + (index + 1)">
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            placeholder="请输入字符串" size="default" v-model="model.value"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请输入小数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input type="integer" placeholder="请输入整数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0">
                                                                    </el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                        <!-- 功能 -->
                        <div v-if="active == 5">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <!-- <template #label>
                                            <span class="custom-tabs-label">
                                                <span slot="label"><span style="color:red;">* </span>基本信息</span>
                                            </span>
                                        </template> -->
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 2">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1 || deviceInfo.status != 3">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <template #append>
                                                    <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 15px"  v-if="item.isReadonly == 0 || deviceInfo.status == 3">指令发送
                                                </el-button>
                                                </template>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="item.isReadonly == 0 || deviceInfo.status == 3 ">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1 || deviceInfo.status != 3">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <template  #append>
                                                                <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 15px"
                                                                v-if="item.isReadonly == 0 || deviceInfo.status == 3 ">指令发送
                                                            </el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <template #append>
                                                                <el-button
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 15px"
                                                                v-if="item.isReadonly == 0 || deviceInfo.status == 3 ">指令发送</el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <template #append>
                                                                <el-button 
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 15px"
                                                                v-if="item.isReadonly == 0 || deviceInfo.status == 3 ">指令发送</el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, index) in item.datatype.arrayModel"
                                                    :key="index" :label="item.name + (index + 1)">
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            placeholder="请输入字符串" size="default" v-model="model.value"
                                                            @input="arrayItemChange($event, item)">
                                                            <template #append>
                                                                <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 15px"
                                                                v-if="item.isReadonly == 0 || deviceInfo.status == 3 ">指令发送
                                                            </el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请输入小数" size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            @input="arrayItemChange($event, item)">
                                                            <template #append>
                                                                <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 15px" 
                                                                v-if=" item.isReadonly  == 0 || deviceInfo.status == 3">指令发送
                                                            </el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input type="integer" placeholder="请输入整数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                            @input="arrayItemChange($event, item)">
                                                           <template #append>
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if=" item.isReadonly  == 0 || deviceInfo.status == 3">
                                                            </el-button>
                                                           </template>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <template #append>
                                                                        <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 15px" 
                                                                        v-if=" param.isReadonly == 0 || deviceInfo.status == 3">指令发送
                                                                    </el-button>
                                                                    </template>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <template #append>
                                                                        <el-button 
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 15px"
                                                                        v-if="param.isReadonly == 0 || deviceInfo.status == 3">指令发送</el-button>
                                                                    </template>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1 || deviceInfo.status != 3">
                                                                   <template #append>
                                                                    <el-button
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 15px"
                                                                        v-if="param.isReadonly == 0 || deviceInfo.status == 3">指令发送</el-button>
                                                                   </template>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                        <!-- 事件 -->
                        <div v-if="active == 6">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 3">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 18px" title="指令发送" v-if="item.isReadonly == 1">
                                                </el-button>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="!valueUnEnable && item.isReadonly == 0">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, index) in item.datatype.arrayModel"
                                                    :key="index" :label="item.name + (index + 1)">
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            placeholder="请输入字符串" size="default" v-model="model.value"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请输入小数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input type="integer" placeholder="请输入整数 " size="default"
                                                            v-model="model.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @input="arrayItemChange($event, item)">
                                                            <el-button @click="mqttPublish(deviceInfo, model)"
                                                                style="font-size: 18px" title="指令发送"
                                                                v-if="!valueUnEnable || item.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0">
                                                                    </el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                        <!-- 关系 -->
                        <div v-if="active == 7">
                            <!-- 设备模式-->
                            <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label"
                                class-name="my-content">
                                <template #label>
                                    <div style="display: flex; align-items: center; ">
                                        <el-icon><ele-Menu /></el-icon>
                                        <div style="margin-left: 10px; ">设备模式</div>
                                    </div>
                                </template>
                                <el-link underline="never"
                                    style="line-height: 28px; font-size: 16px; padding-right: 10px">{{
                                        title }}</el-link>
                            </el-descriptions-item>
                            <div v-for="(item, index) in deviceInfo.thingsModels" :key="index">
                                <div v-if="item.type == 4">
                                    <el-descriptions-item :labelStyle="statusColor" label-class-name="my-label">
                                        <template #label>
                                            <el-icon><ele-Open /></el-icon>
                                            {{ item.name }}
                                        </template>
                                        <!-- 设备开关 -->
                                        <div v-if="item.datatype.type == 'bool'">
                                            <el-switch v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                @change="mqttPublish(deviceInfo, item)" active-text="" inactive-text=""
                                                active-value="1" inactive-value="0" style="min-width: 100px" />
                                        </div>
                                        <!-- 射频遥控 / 运行档位 /上报挡位 /设备重启 / 状态灯色 -->
                                        <div v-if="item.datatype.type == 'enum'">
                                            <!-- 上报挡位 -->
                                            <div v-if="item.datatype.showWay && item.datatype.showWay == 'button'">
                                                <el-button style="margin: 5px" size="default"
                                                    :disabled="item.isReadonly == 1"
                                                    @click="enumButtonClick(deviceInfo, item, subItem.value)"
                                                    v-for="subItem in item.datatype.enumList" :key="subItem.value">
                                                    {{ subItem.text }}
                                                </el-button>
                                            </div>
                                            <!-- 射频遥控 -->
                                            <el-select v-else v-model="item.value" placeholder="请选择"
                                                @change="mqttPublish(deviceInfo, item)"
                                                :disabled="item.isReadonly == 1">
                                                <el-option v-for="subItem in item.datatype.enumList"
                                                    :key="subItem.value" :label="subItem.text" :value="subItem.value" />
                                            </el-select>
                                        </div>
                                        <!-- 电源管理/ 屏显消息 -->
                                        <div v-if="item.datatype.type == 'string'">
                                            <el-input v-model="item.value"
                                                :disabled="item.isReadonly == 1"
                                                :placeholder="'请输入字符串 ' + (item.datatype.unit ? '，单位：' + item.datatype.unit : '')">
                                                <el-button @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 18px" title="指令发送" v-if="item.isReadonly == 1">
                                                </el-button>
                                            </el-input>
                                        </div>
                                        <div v-if="item.datatype.type == 'decimal'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; padding: 1px 8px; margin: 2px 0 0 5px; border-radius: 3px"
                                                    title="指令发送"
                                                    v-if="!valueUnEnable && item.isReadonly == 0"></el-button>
                                            </div>
                                        </div>
                                        <!-- 上报监测数据 -->
                                        <div v-if="item.datatype.type == 'integer'">
                                            <div style="width: 80%; float: left">
                                                <el-slider v-model="item.value" :min="item.datatype.min"
                                                    :max="item.datatype.max" :step="item.datatype.step"
                                                    :format-tooltip="(x: string) => x + ' ' + item.datatype.unit"
                                                    :disabled="item.isReadonly == 1"></el-slider>
                                            </div>
                                            <div style="width: 20%; float: left">
                                                <el-button type="info" @click="mqttPublish(deviceInfo, item)"
                                                    style="font-size: 16px; height: 27px; padding: 1px 12px 0 16px; margin: 5px 0px 0px 10px;; border-radius: 3px"
                                                    title="指令发送" v-if="!valueUnEnable && item.isReadonly == 0">
                                                </el-button>
                                            </div>
                                        </div>
                                        <!-- 功能分组 -->
                                        <div v-if="item.datatype.type == 'object'">
                                            <el-descriptions :column="1" size="default" border>
                                                <el-descriptions-item v-for="(param, index) in item.datatype.params"
                                                    :key="index" :label="param.name" label-width="120px">
                                                    <div v-if="param.datatype.type == 'bool'">
                                                        <el-switch v-model="param.value"
                                                            :disabled="item.isReadonly == 1"
                                                            @change="mqttPublish(deviceInfo, param)" active-text=""
                                                            inactive-text="" active-value="1" inactive-value="0"
                                                            style="min-width: 100px" />
                                                    </div>
                                                    <div v-if="param.datatype.type == 'enum'">
                                                        <div
                                                            v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                            <el-button style="margin: 5px" size="default"
                                                                @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value"
                                                                :disabled="item.isReadonly == 1">
                                                                {{ subItem.text }}
                                                            </el-button>
                                                        </div>
                                                        <el-select size="default" v-else v-model="param.value"
                                                            placeholder="请选择" @change="mqttPublish(deviceInfo, param)"
                                                            :disabled="item.isReadonly == 1">
                                                            <el-option v-for="subItem in param.datatype.enumList"
                                                                :key="subItem.value" :label="subItem.text"
                                                                :value="subItem.value" />
                                                        </el-select>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'string'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" placeholder="请输入字符串">
                                                            <el-button @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0">
                                                            </el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'decimal'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="number" placeholder="请输入小数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                    <div v-if="param.datatype.type == 'integer'">
                                                        <el-input
                                                            :disabled="item.isReadonly == 1"
                                                            v-model="param.value" type="integer" placeholder="请输入整数 ">
                                                            <el-button slot="append"
                                                                @click="mqttPublish(deviceInfo, param)"
                                                                style="font-size: 20px" title="指令发送"
                                                                v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                        </div>
                                        <!-- 灯光色值 -->
                                        <div v-if="item.datatype.type == 'array'">
                                            <el-descriptions :column="1" size="default" border
                                                v-if="item.datatype.arrayType != 'object'">
                                                <el-descriptions-item v-for="(model, indexs) in item.datatype.arrayModel"
                                                    :key="indexs" :label="item.name + (indexs + 1)">
                                                    <!-- 字符串 -->
                                                    <div v-if="item.datatype.arrayType == 'string'">
                                                        <el-input readonly   @click="NavigatePage(model.value)"
                                                            placeholder="请点击选择关联设备" size="default" v-model="model.deviceName" >
                                                            <template #append v-if="!model.value">
                                                                <el-button  :disabled="item.isReadonly == 1" @click="openDevice(index,indexs)">
                                                                    选择关联设备
                                                                </el-button>
                                                            </template>
                                                           <template #append v-if="model.value != ''">
                                                                <el-button  :disabled="item.isReadonly == 1" @click="clear(index,indexs)">
                                                                    清空
                                                                </el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                    <!-- 小数 -->
                                                    <div v-if="item.datatype.arrayType == 'decimal'">
                                                        <el-input type="number" placeholder="请点击选择关联设备 " size="default"
                                                            v-model="model.deviceName"  readonly  @click="NavigatePage(model.value)">
                                                            <template #append v-if="!model.value">
                                                                <el-button  :disabled="item.isReadonly == 1" @click="openDevice(index,indexs)">
                                                                    选择关联设备
                                                                </el-button>
                                                            </template>
                                                           <template #append v-if="model.value != ''">
                                                                <el-button  :disabled="item.isReadonly == 1" @click="clear(index,indexs)">
                                                                    清空
                                                                </el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                    <!-- 整数 -->
                                                    <div v-if="item.datatype.arrayType == 'integer'">
                                                        <el-input style="width: 400px; height: 35px;" type="integer" placeholder="请点击选择关联设备" size="default"
                                                            v-model="model.deviceName" readonly @click="NavigatePage(model.value)">
                                                            <template #append v-if="!model.value">
                                                                <el-button  :disabled="item.isReadonly == 1" @click="openDevice(index,indexs)">
                                                                    选择关联设备
                                                                </el-button>
                                                            </template>
                                                           <template #append v-if="model.value != ''">
                                                                <el-button  :disabled="item.isReadonly == 1" @click="clear(index,indexs)">
                                                                    清空
                                                                </el-button>
                                                            </template>
                                                        </el-input>
                                                    </div>
                                                </el-descriptions-item>
                                            </el-descriptions>
                                            <el-collapse v-if="item.datatype.arrayType == 'object'">
                                                <el-collapse-item
                                                    v-for="(arrayParam, index) in item.datatype.arrayParams"
                                                    :key="index">
                                                    <template #title>
                                                        <span style="color: #666">
                                                            <el-icon><ele-Tickets /></el-icon>
                                                            {{ item.name + (index + 1) }}
                                                        </span>
                                                    </template>
                                                    <el-descriptions :column="1" size="default" border>
                                                        <el-descriptions-item v-for="(param, index) in arrayParam"
                                                            :key="index" :label="param.name">
                                                            <div v-if="param.datatype.type == 'bool'">
                                                                <el-switch v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    @change="mqttPublish(deviceInfo, param)"
                                                                    active-text="" inactive-text="" active-value="1"
                                                                    inactive-value="0" style="min-width: 100px" />
                                                            </div>
                                                            <div v-if="param.datatype.type == 'enum'">
                                                                <div
                                                                    v-if="param.datatype.showWay && param.datatype.showWay == 'button'">
                                                                    <el-button style="margin: 5px" size="default"
                                                                        :disabled="item.isReadonly == 1"
                                                                        @click="enumButtonClick(deviceInfo, param, subItem.value)"
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value">
                                                                        {{ subItem.text }}
                                                                    </el-button>
                                                                </div>
                                                                <el-select v-else v-model="param.value"
                                                                    :disabled="item.isReadonly == 1"
                                                                    placeholder="请选择" size="small"
                                                                    @change="mqttPublish(deviceInfo, param)">
                                                                    <el-option
                                                                        v-for="subItem in param.datatype.enumList"
                                                                        :key="subItem.value" :label="subItem.text"
                                                                        :value="subItem.value" />
                                                                </el-select>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'string'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" placeholder="请输入字符串">
                                                                    <el-button @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0">
                                                                    </el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'decimal'">
                                                                <el-input
                                                                    :disabled="item.isReadonly == 1"
                                                                    v-model="param.value" type="number"
                                                                    placeholder="请输入小数 ">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                            <div v-if="param.datatype.type == 'integer'">
                                                                <el-input v-model="param.value" type="integer"
                                                                    placeholder="请输入整数 "
                                                                    :disabled="item.isReadonly == 1">
                                                                    <el-button slot="append"
                                                                        @click="mqttPublish(deviceInfo, param)"
                                                                        style="font-size: 20px" title="指令发送"
                                                                        v-if="!valueUnEnable && param.isReadonly == 0"></el-button>
                                                                </el-input>
                                                            </div>
                                                        </el-descriptions-item>
                                                    </el-descriptions>
                                                </el-collapse-item>
                                            </el-collapse>
                                        </div>
                                        <!-- 现场图片 -->
                                        <div v-if="item.datatype.type == 'file'">
                                            <imageUpload ref="image-upload"
                                                :isDisabled="item.isReadonly == 1"
                                                v-model:model-value="item.value" :limit="1" :fileSize="1">
                                            </imageUpload>
                                        </div>
                                        
                                    </el-descriptions-item>
                                </div>
                            </div>
                        </div>
                    </el-descriptions>
<!--                    <el-table :data="thingsModelList" border v-if="active == 7" >-->
<!--                        <el-table-column label="物模型名称" prop="name" align="center" width="180">-->
<!--                            <template #default="scope">-->
<!--                                {{ scope.row.name }}-->

<!--                            </template>   -->
<!--                        </el-table-column>-->
<!--                        <el-table-column label="关系"  align="center">-->
<!--                            <template #default="{ row }">-->
<!--                               <el-table :data="row.datatypes" border >-->
<!--                                    <el-table-column label="设备名称" prop="deviceName" align="center">-->
<!--                                        <template #default="scope">-->
<!--                                                {{ scope.row.deviceName }}-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                                    <el-table-column label="遥测属性"  align="center">-->
<!--                                        <template #default="{row}">-->
<!--                                            <el-table :data="row.params" border >-->
<!--                                                    <el-table-column label="名称" prop="name" align="center">-->
<!--                                                    </el-table-column>-->
<!--                                                    <el-table-column label="创建时间" prop="ts" align="center"> </el-table-column>-->
<!--                                                    <el-table-column label="值" prop="value" align="center"></el-table-column>-->
<!--                                            </el-table>-->
<!--                                        </template>-->
<!--                                    </el-table-column>-->
<!--                               </el-table>-->
<!--                                {{ row.deviceName }}-->
<!--                            </template>-->
<!--                        </el-table-column>-->
<!--                    </el-table>-->
                </div>
            </el-col>

        </el-row>
        <DeviceList ref="DeviceListRef" @deviceEvent="getSelecttDevice" />
    </div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, nextTick, onUnmounted, ref, watch } from 'vue';
import * as echarts from 'echarts';
import mqttTool from '/@/utils/mqttTool';
import { serviceInvoke } from '/@/api/iot/runstatus';
import { ElMessage } from 'element-plus';
import imageUpload from '/@/components/ImageUpload/index.vue'
import { listDeviceByGroup, ListThingsModel } from '/@/api/iot/device';
import router from '/@/router';
const DeviceList = defineAsyncComponent(() => import('/@/views/iot/device/device-list.vue'));
const DeviceListRef = ref();
const active = ref(0)
// 定义 props
const props = defineProps({
    modelValue: {
        type: Object
    }
})
const title = ref('设备控制')// 控制模块标题
const valueUnEnable = ref(false) // 未启用设备影子
// 控制项标题背景
const statusColor = ref({
    background: '#67C23A',
    color: '#fff',
    minWidth: '100px',
})
// 设备信息
const deviceInfo = ref<any>({
    chartList: [],
})
// 监测图表
const monitorChart = ref<any[]>([
    {
        chart: {} as any,
        data: {
            id: '',
            name: '',
            value: '' as any,
        },
    },
])
// 用于存储所有图表的 ref
const chartRefs = ref<HTMLElement[]>([]);
// 动态设置 ref
const setChartRef = (el: any) => {
    if (el) {
        chartRefs.value.push(el);
    }
};
const thingsModelList = ref<{ datatypes: any }[]>([]) // 物模型列表
const activeChange = (index:any) =>{
    active.value = index as any
    if(index == 7){
        if(props.modelValue){
            ListThingsModel(props.modelValue.deviceId).then((response) => {
                thingsModelList.value = response.data.data.thingsModels
                thingsModelList.value  = thingsModelList.value.filter(item => item.datatypes != null);
        });
        }
    }
    // 当切换到遥测属性标签页时，初始化图表
    if(index == 2 && deviceInfo.value.chartList && deviceInfo.value.chartList.length > 0){
        nextTick(() => {
            // 等待 DOM 完全渲染后再初始化图表
            setTimeout(() => {
                MonitorChart();
            }, 100);
        });
    }
}
/* Mqtt回调处理 */
const mqttCallback = () => {
    if (mqttTool.client) {
        mqttTool.client.on('message', (topic, message) => {
            let topics = topic.split('/');
            let deviceNum = topics[2];
            const parsedMessage = JSON.parse(message.toString());
            if (!parsedMessage) {
                return;
            }
            if (topics[3] == 'status') {
                // 更新列表中设备的状态
                if (deviceInfo.value.serialNumber == deviceNum) {
                    deviceInfo.value.status = parsedMessage.status;
                    deviceInfo.value.isvalue = parsedMessage.isvalue;
                    deviceInfo.value.rssi = parsedMessage.rssi;
                    updateDeviceStatus(deviceInfo.value);
                }
            }
            //兼容设备回复
            if (topics[4] == 'reply') {
                // this.$modal.notifySuccess(parsedMessage);
                ElMessage.success(parsedMessage)
            }
            if (topic.endsWith('monitor/post')) {
                // 更新列表中设备的属性
                if (deviceInfo.value.serialNumber == deviceNum) {
                    for (let j = 0; j < parsedMessage.length; j++) {
                        let isComplete = false;
                        // 设备状态
                        for (let k = 0; k < deviceInfo.value.thingsModels.length && !isComplete; k++) {
                            if (deviceInfo.value.thingsModels[k].id == parsedMessage[j].id) {
                                // 普通类型(小数/整数/字符串/布尔/枚举)
                                if (deviceInfo.value.thingsModels[k].datatype.type == 'decimal' || deviceInfo.value.thingsModels[k].datatype.type == 'integer') {
                                    deviceInfo.value.thingsModels[k].value = Number(parsedMessage[j].value);
                                } else {
                                    deviceInfo.value.thingsModels[k].value = parsedMessage[j].value;
                                }
                                isComplete = true;
                                break;
                            } else if (deviceInfo.value.thingsModels[k].datatype.type == 'object') {
                                // 对象类型
                                for (let n = 0; n < deviceInfo.value.thingsModels[k].datatype.params.length; n++) {
                                    if (deviceInfo.value.thingsModels[k].datatype.params[n].id == parsedMessage[j].id) {
                                        deviceInfo.value.thingsModels[k].datatype.params[n].value = parsedMessage[j].value;
                                        isComplete = true;
                                        break;
                                    }
                                }
                            } else if (deviceInfo.value.thingsModels[k].datatype.type == 'array') {
                                // 数组类型
                                if (deviceInfo.value.thingsModels[k].datatype.arrayType == 'object') {
                                    // 1.对象类型数组,id为数组中一个元素,例如：array_01_gateway_temperature
                                    if (String(parsedMessage[j].id).indexOf('array_') == 0) {
                                        for (let n = 0; n < deviceInfo.value.thingsModels[k].datatype.arrayParams.length; n++) {
                                            for (let m = 0; m < deviceInfo.value.thingsModels[k].datatype.arrayParams[n].length; m++) {
                                                if (deviceInfo.value.thingsModels[k].datatype.arrayParams[n][m].id == parsedMessage[j].id) {
                                                    deviceInfo.value.thingsModels[k].datatype.arrayParams[n][m].value = parsedMessage[j].value;
                                                    isComplete = true;
                                                    break;
                                                }
                                            }
                                            if (isComplete) {
                                                break;
                                            }
                                        }
                                    } else {
                                        // 2.对象类型数组，例如：gateway_temperature,消息ID添加前缀后匹配
                                        for (let n = 0; n < deviceInfo.value.thingsModels[k].datatype.arrayParams.length; n++) {
                                            for (let m = 0; m < deviceInfo.value.thingsModels[k].datatype.arrayParams[n].length; m++) {
                                                let index = n > 9 ? String(n) : '0' + k;
                                                let prefix = 'array_' + index + '_';
                                                if (deviceInfo.value.thingsModels[k].datatype.arrayParams[n][m].id == prefix + parsedMessage[j].id) {
                                                    deviceInfo.value.thingsModels[k].datatype.arrayParams[n][m].value = parsedMessage[j].value;
                                                    isComplete = true;
                                                }
                                            }
                                            if (isComplete) {
                                                break;
                                            }
                                        }
                                    }
                                } else {
                                    // 整数、小数和字符串类型数组
                                    for (let n = 0; n < deviceInfo.value.thingsModels[k].datatype.arrayModel.length; n++) {
                                        if (deviceInfo.value.thingsModels[k].datatype.arrayModel[n].id == parsedMessage[j].id) {
                                            deviceInfo.value.thingsModels[k].datatype.arrayModel[n].value = parsedMessage[j].value;
                                            isComplete = true;
                                            break;
                                        }
                                    }
                                }                                
                            }
                        }
                        // 图表数据
                        for (let k = 0; k < deviceInfo.value.chartList.length; k++) {
                            if (deviceInfo.value.chartList[k].id.indexOf('array_') == 0) {
                                // 数组类型匹配,例如：array_00_gateway_temperature
                                if (deviceInfo.value.chartList[k].id == parsedMessage[j].id) {
                                    deviceInfo.value.chartList[k].value = parsedMessage[j].value;

                                    // 更新图表
                                    for (let m = 0; m < monitorChart.value.length; m++) {
                                        if (parsedMessage[j].id == monitorChart.value[m].data.id) {
                                            let data = [
                                                {
                                                    value: parsedMessage[j].value,
                                                    name: monitorChart.value[m].data.name,
                                                },
                                            ];
                                            monitorChart.value[m].chart.setOption({
                                                series: [
                                                    {
                                                        data: data,
                                                    },
                                                ],
                                            });
                                            break;
                                        }
                                    }
                                }
                            } else {
                                // 普通类型匹配
                                if (deviceInfo.value.chartList[k].id == parsedMessage[j].id) {
                                    deviceInfo.value.chartList[k].value = parsedMessage[j].value;
                                    // 更新图表
                                    for (let m = 0; m < monitorChart.value.length; m++) {
                                        if (parsedMessage[j].id == monitorChart.value[m].data.id) {
                                            isComplete = true;
                                            let data = [
                                                {
                                                    value: parsedMessage[j].value,
                                                    name: monitorChart.value[m].data.name,
                                                },
                                            ];
                                            monitorChart.value[m].chart.setOption({
                                                series: [
                                                    {
                                                        data: data,
                                                    },
                                                ],
                                            });
                                            break;
                                        }
                                    }
                                }
                            }
                            if (isComplete) {
                                break;
                            }
                        }
                    }
                }
            }
        });
    }
}
/** 更新设备状态 */
const updateDeviceStatus = (device: any) => {
    if (device.status == 3) {
        statusColor.value.background = '#12d09f';
        title.value = '在线模式';
    } else {
        if (device.isvalue == 1) {
            statusColor.value.background = '#409EFF';
            title.value = '影子模式';
        } else {
            statusColor.value.background = '#909399';
            title.value = '离线模式';
            valueUnEnable.value = true;
        }
    }
}
/** 物模型数组元素值改变事件 */
const arrayItemChange = (value: any, thingsModel: any) => {
    console.log(thingsModel, 'thingsModel');

    let shadow = '';
    for (let i = 0; i < thingsModel.datatype.arrayCount; i++) {
        shadow += thingsModel.datatype.arrayModel[i].value + ',';
    }
    shadow = shadow.substring(0, shadow.length - 1);
    thingsModel.value = shadow;
    
}
/** 枚举类型按钮单击 */
const enumButtonClick = (device: any, model: any, value: any) => {
    model.shadow = value;
    mqttPublish(device, model);
}
//发送指令
const mqttPublish = (device: any, model: any) => {
    if(active.value == 5){
        const command: Record<string, any> = {};
    const num = model.id
    command[num] = model.shadow
    const data = {
        serialNumber: device.serialNumber,
        productId: device.productId,
        remoteCommand: command,
        identifier: model.id,
        modelName: model.name,
        isShadow: device.status != 3,
        type: model.type,
    };
    serviceInvoke(data).then((response) => {
        if (response.data.code === 200) {
            ElMessage.success({
                type: 'success',
                message: '服务调用成功!',
            });
        }
    });
    }
}
// 获取选择关联设备的信息
const getSelecttDevice = (device:any,valueIndex:any) =>{
let arr = deviceInfo.value.thingsModels[valueIndex.index].value.split(',')
// 确保数组的长度足够，可以安全插入数据
while (arr.length <= valueIndex.indexs) {
        arr.push('');
    }
    // 插入数据到指定的索引位置
    arr[valueIndex.indexs] = device.deviceId;
    deviceInfo.value.thingsModels[valueIndex.index].value = arr.join(',');
    deviceInfo.value.thingsModels[valueIndex.index].datatype.arrayModel[valueIndex.indexs].value = String(device.deviceId)
    deviceInfo.value.thingsModels[valueIndex.index].datatype.arrayModel[valueIndex.indexs].deviceName = device.deviceName
    
}
// 打开择关联设备弹窗
const openDevice = (index:any,indexs:any) =>{
    DeviceListRef.value.openDialog()
    DeviceListRef.value.valueIndex.index = index
    DeviceListRef.value.valueIndex.indexs = indexs
    
}
// 清空
const clear = (index:any,indexs:any) =>{
    let arr = deviceInfo.value.thingsModels[index].value.split(',')
// 确保数组的长度足够，可以安全插入数据
while (arr.length <= indexs) {
        arr.push('');
    }
    // 插入数据到指定的索引位置
    arr[indexs] = ''
    deviceInfo.value.thingsModels[index].value = arr.join(',');
    deviceInfo.value.thingsModels[index].datatype.arrayModel[indexs].value = ''
    deviceInfo.value.thingsModels[index].datatype.arrayModel[indexs].deviceName = ''   
}
// 跳转页面
const NavigatePage =(deviceId:any) =>{
    if(deviceId){
        router.push({
            path: '/iot/device-edit',
            query: {
                deviceId: deviceId,
                isSubDev: 0,
                pageNum: 1,
                activeName: 'basic',
            },
        });
    }
}

const MonitorChart = () => {
    // 清理之前的图表实例
    monitorChart.value.forEach(item => {
        if (item.chart && typeof item.chart.dispose === 'function') {
            item.chart.dispose();
        }
    });
    monitorChart.value = [];

    for (let i = 0; i < deviceInfo.value.chartList.length; i++) {
        const chartElement = chartRefs.value[i];

        // 检查 DOM 元素是否存在
        if (!chartElement) {
            console.warn(`Chart element at index ${i} not found`);
            continue;
        }

        // 检查元素是否可见和有尺寸
        if (chartElement.clientWidth === 0 || chartElement.clientHeight === 0) {
            console.warn(`Chart element at index ${i} has zero dimensions, setting default size`);
            // 强制设置尺寸
            chartElement.style.width = '185px';
            chartElement.style.height = '230px';
            chartElement.style.display = 'block';
            // 强制重新计算布局
            chartElement.offsetHeight;
        }

        try {
            monitorChart.value[i] = {
                chart: echarts.init(chartElement),
                data: {
                    id: deviceInfo.value.chartList[i].id,
                    name: deviceInfo.value.chartList[i].name,
                    value: deviceInfo.value.chartList[i].value ? deviceInfo.value.chartList[i].value : deviceInfo.value.chartList[i].datatype.min,
                },
            };
        } catch (error) {
            console.error(`Failed to initialize chart at index ${i}:`, error);
            continue;
        }
        var option;
        option = {
            tooltip: {
                formatter: ' {b} <br/> {c}' + deviceInfo.value.chartList[i].datatype.unit,
            },
            series: [
                {
                    name: deviceInfo.value.chartList[i].datatype.type,
                    type: 'gauge',
                    min: deviceInfo.value.chartList[i].datatype.min,
                    max: deviceInfo.value.chartList[i].datatype.max,
                    colorBy: 'data',
                    splitNumber: 10,
                    radius: '100%',
                    // 分割线
                    splitLine: {
                        distance: 4,
                    },
                    axisLabel: {
                        fontSize: 10,
                        distance: 10,
                    },
                    // 刻度线
                    axisTick: {
                        distance: 4,
                    },
                    // 仪表盘轴线
                    axisLine: {
                        lineStyle: {
                            width: 8,
                            color: [
                                [0.2, '#409EFF'], // 0~20%
                                [0.8, '#12d09f'], // 40~60%
                                [1, '#F56C6C'], // 80~100%
                            ],
                            opacity: 0.3,
                        },
                    },
                    pointer: {
                        icon: 'triangle',
                        length: '60%',
                        width: 7,
                    },
                    progress: {
                        show: true,
                        width: 8,
                    },
                    detail: {
                        valueAnimation: true,
                        formatter: '{value}' + ' ' + deviceInfo.value.chartList[i].datatype.unit,
                        offsetCenter: [0, '80%'],
                        fontSize: 20,
                    },
                    data: [
                        {
                            value: deviceInfo.value.chartList[i].value ? deviceInfo.value.chartList[i].value : deviceInfo.value.chartList[i].datatype.min,
                            name: deviceInfo.value.chartList[i].name,
                        },
                    ],
                    title: {
                        offsetCenter: [0, '115%'],
                        fontSize: 16,
                    },
                },
            ],
        };
        option && monitorChart.value[i].chart.setOption(option);
    }
    
}
// 监听props的变化
watch(() => props.modelValue, (newVal) => {
    try {
        if (newVal && newVal.deviceId != 0) {
            deviceInfo.value = newVal;
            updateDeviceStatus(deviceInfo.value);
            for (let i = 0; i < deviceInfo.value.thingsModels.length; i++) {
                if(deviceInfo.value.thingsModels[i].type == 4){
                    for (let j = 0; j < deviceInfo.value.thingsModels[i].datatype.arrayModel.length; j++) {
                        if(deviceInfo.value.thingsModels[i].datatype.arrayModel[j].value){
                            listDeviceByGroup({ deviceId: deviceInfo.value.thingsModels[i].datatype.arrayModel[j].value }).then(res => {
                                deviceInfo.value.thingsModels[i].datatype.arrayModel[j].deviceName = res.data.rows[0].deviceName
                        })
                        }
                        
                    }

                }
                
            }
            // 清空之前的 chartRefs，图表只在用户点击"遥测属性"标签时初始化
            chartRefs.value = [];
            mqttCallback();
        }
    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });

// 组件销毁时清理图表实例
onUnmounted(() => {
    monitorChart.value.forEach(item => {
        if (item.chart && typeof item.chart.dispose === 'function') {
            item.chart.dispose();
        }
    });
    monitorChart.value = [];
    chartRefs.value = [];
});
</script>
<style scoped>
/* 重写滑动块样式 */
.el-slider__bar {
    height: 18px;
}

.el-slider__runway {
    height: 18px;
    margin: 5px 0;
}

.el-slider__button {
    height: 18px;
    width: 18px;
    border-radius: 10%;
}

.el-slider__button-wrapper {
    top: -9px;
}

:deep(.my-label) {
    color: #ffffff !important;
    background-color: #409eff !important;
    min-width: 150px;
}

/* :deep(.my-content) {
    background-color: #409eff;
} */
 .tabs{
    width: 1000px; 
    display: flex;
    justify-content: space-between;
    padding: 30px 0px;
 }
.tab{
    border: 1px solid #ffffff;
    width: 100px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
    border-radius: 5px;
    background: #e4e7ed;
}
.tab:hover {
    background: #d1d8e0;  /* 鼠标悬停时的效果 */
    border-color: #999999;
}

.actives {
    color: #ffffff;
    border: 1px solid #409eff;
    background-color: #409eff;
    transition: all 0.003s ease; /* 选中项的过渡效果 */
}
/* 可选：为所有 tab 添加 hover 动画 */
.tab:not(.actives):hover {
    background: #c6d1e6;
    border-color: #d0d0d0;
}
:deep(.el-select__wrapper) {
    border: none;
    min-height: 35px;
}
</style>